import { Button, RadioGroup, useDisclosure } from "@heroui/react";
import { Bi<PERSON><PERSON>cil, BiTrash } from "react-icons/bi";
import ModalComponent from "../ModalComponent";
import { FiAlertTriangle } from "react-icons/fi";
import { OrderSchema } from "../../types/orderType";
import { FieldErrors, UseFormSetValue } from "react-hook-form";
import CustomerFormModal from "../customers/CustomerFormModal";
import { AddressType, CustomerAddressType, CustomerType } from "../../types/customerType";
import { useCustomerFormModal } from "../../hooks/useCustomerFormModal";
import { CustomRadio } from "../forms/CustomRadio";
import { useState } from "react";

interface SelectedCustomerProps {
	setValue: UseFormSetValue<OrderSchema>;
	customerId: string;
	users: CustomerType[];
	errors: FieldErrors<OrderSchema>;
}

const AddressRadio = ({ customerAddress }: { customerAddress: CustomerAddressType }) => {
	return (
		<CustomRadio value={customerAddress._id} endContent={customerAddress.addressType}>
			<div className="flex flex-col">
				<span className="text-xs text-default-500">
					{customerAddress.addressline1}, {customerAddress.addressline2}, {customerAddress.landmark}
					, {customerAddress.city}, {customerAddress.states}, {customerAddress.pincode},{" "}
					{customerAddress.country}
				</span>
				<span className="text-xs text-default-500">
					{customerAddress.countryCode ? `${customerAddress.countryCode} ` : ""}
					{customerAddress.phone}
				</span>
			</div>
		</CustomRadio>
	);
};

const SelectedCustomer = ({
	setValue,
	errors,
	users = [],
	customerId = "",
}: SelectedCustomerProps) => {
	const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
	const [selectedAddress, setSelectedAddress] = useState("");
	// Use the customer form modal hook
	const { customerFormModal, openEditModal } = useCustomerFormModal();
	const handleDeleteCustomer = () => {
		console.log(" dlete the cutomer");
		setValue("userData.userId", "");
		setValue("shippingAddressId", "");
		setValue("shippingAddress", {
			addressType: AddressType.HOME,
			flat: 0,
			addressline1: "",
			addressline2: "",
			landmark: "",
			//countryCode: "",
			phone: "",
			city: "",
			country: "",
			states: "",
			pincode: "",
			primary: true,
		});
		onClose();
	};

	if (customerId === "") return null;
	const customer = users.find((user) => user._id === customerId);
	console.log(customer, "customer data");

	// steps
	// need to fetch the customer details from the grapqhl query
	// and also add the edit and delete button here
	// and also add the customer details here

	// when clicked on the edit button, it should open the customer edit modal
	// when clicked on the delete button, it should open the customer delete modal and i need to delete the selectedCustomerId from here stored in the orders slice.
	return (
		<>
			<div className="flex w-full justify-between items-center">
				<ModalComponent
					saveButtonText={"Delete"}
					isOpen={isOpen}
					onOpenChange={onOpenChange}
					onPress={handleDeleteCustomer}
				>
					<div className="w-full flex flex-col gap-4 items-center justify-center p-4">
						<FiAlertTriangle className="text-3xl text-danger" />
						<h5>Are you sure you want to remove the customer?</h5>
					</div>
				</ModalComponent>
				<div>
					<h5 className="text-primary font-semibold">{`${customer?.firstName} ${customer?.lastName}`}</h5>
					<small>{customer?.email}</small>
				</div>
				<div className="flex gap-2">
					{" "}
					<Button
						isIconOnly
						color="primary"
						variant="ghost"
						className="border w-6 h-6 min-w-6 min-h-6 rounded-full"
						size="sm"
						onPress={() => customer && openEditModal(customer)}
						radius="full"
						startContent={<BiPencil />}
					/>
					<Button
						isIconOnly
						color="primary"
						variant="ghost"
						className="border w-6 h-6 min-w-6 min-h-6 rounded-full"
						size="sm"
						radius="full"
						onPress={onOpen}
						startContent={<BiTrash />}
					/>
				</div>

				<CustomerFormModal
					isOpen={customerFormModal.isOpen}
					onOpenChange={customerFormModal.onOpenChange}
					initialData={customer}
					isEditMode={true}
					isLoading={false}
				/>
			</div>
			<div>
				{customer ? (
					<div className="mt-10">
						<RadioGroup
							value={selectedAddress}
							onValueChange={(value) => {
								setSelectedAddress(value);
								setValue("shippingAddressId", value);
								setValue(
									"shippingAddress",
									customer?.addresses.find((address) => address._id === value) || {
										addressType: AddressType.HOME,
										flat: 0,
										addressline1: "",
										addressline2: "",
										landmark: "",
										//countryCode: "",
										phone: "",
										city: "",
										country: "",
										states: "",
										pincode: "",
										primary: true,
									}
								);
							}}
							label="Select address for delivery"
							orientation="vertical"
						>
							{customer?.addresses.map((address) => (
								<AddressRadio key={address._id} customerAddress={address} />
							))}
						</RadioGroup>
						<div>
							<small className="text-red-500">
								{errors.shippingAddress ? "Shipping address is required" : ""}
							</small>
						</div>
					</div>
				) : null}
			</div>
		</>
	);
};

export default SelectedCustomer;
