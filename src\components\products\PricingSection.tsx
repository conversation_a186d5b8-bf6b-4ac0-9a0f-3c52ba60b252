import { Input, NumberInput, Switch, Tab, Tabs } from "@heroui/react";
import React from "react";
import {
  Controller,
  UseFormResetField,
  UseFormSetValue,
} from "react-hook-form";
import { ProductSchema, SaleTypeEnum } from "../../types/productType";

type PricingSectionProps = {
  control: any;
  errors: any;
  setFormValue: UseFormSetValue<ProductSchema>;
  watch: any;
  getValues: (name: string) => any;
  resetField: UseFormResetField<ProductSchema>;
  register: any;
};

const PricingSection = ({
  control,
  errors,
  setFormValue,
  getValues,
  watch,
  resetField,
  register,
}: PricingSectionProps) => {
  const isOnSale = watch("isOnSale");
  const price = Number(watch("price")) || 0;
  const costOfGoods = Number(watch("costOfGoods")) || 0;
  const saleType = watch("saleType") as SaleTypeEnum;

  // --- Helper ---
  const calculateDiscountedPrice = () => {
    const discount = Number(watch("saleValue")) || 0;
    const saleType = watch("saleType") || "PERCENT";
    if (saleType === "PERCENT") {
      return price - (price * discount) / 100;
    }
    return price - discount;
  };

  const calculateProfit = (discountedPrice: number, cost: number) => {
    return Math.max(0, discountedPrice - cost);
  };

  const calculateMargin = (profit: number, discountedPrice: number) => {
    return discountedPrice > 0 ? (profit / discountedPrice) * 100 : 0;
  };

  // --- When costOfGoods changes ---
  const handleCostOfGoodsChange = (
    value: number | React.ChangeEvent<HTMLInputElement>
  ) => {
    const cost = typeof value === "number" ? value : Number(value.target.value);
    const discountedPrice = calculateDiscountedPrice();
    const profit = calculateProfit(discountedPrice, cost);
    const margin = calculateMargin(profit, discountedPrice);

    setFormValue("costOfGoods", cost);
    setFormValue("profit", profit);
    setFormValue("margin", margin);
  };

  // --- When saleValue or saleType changes ---
  const handleDiscountChange = () => {
    const discountedPrice = calculateDiscountedPrice();
    const profit = calculateProfit(discountedPrice, costOfGoods);
    const margin = calculateMargin(profit, discountedPrice);

    setFormValue("discountedPrice", Math.max(0, discountedPrice));
    setFormValue("profit", profit);
    setFormValue("margin", margin);
  };

  return (
    <div className="flex flex-col gap-y-3">
      {/* Price */}
      <Controller
        name="price"
        control={control}
        render={({ field: { onChange, onBlur, value, ref } }) => (
          <NumberInput
            ref={ref}
            label="Price"
            onValueChange={(val) => {
              onChange(val);
              setFormValue("price", val);
              setFormValue("discountedPrice", val);
              setFormValue("profit", val - costOfGoods);
              setFormValue("margin", calculateMargin(val - costOfGoods, val));
            }}
            onBlur={onBlur}
            value={value}
            isRequired
            labelPlacement="outside"
            startContent="₹"
            errorMessage={errors.price?.message}
            isInvalid={!!errors.price}
            classNames={{
              inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md",
            }}
          />
        )}
      />

      {/* Sale Toggle */}
      <div className="flex flex-col gap-y-4">
        <Switch
          isSelected={isOnSale}
          isDisabled={price <= 0}
          onValueChange={() => {
            const newValue = !isOnSale;
            setFormValue("isOnSale", newValue);

            if (newValue) {
              // Enable Sale
              resetField("saleValue");
              const discountedPrice = calculateDiscountedPrice();
              const profit = calculateProfit(discountedPrice, costOfGoods);
              const margin = calculateMargin(profit, discountedPrice);
              setFormValue("discountedPrice", discountedPrice);
              setFormValue("profit", profit);
              setFormValue("margin", margin);
            } else {
              // Disable Sale
              setFormValue("saleValue", null);
              setFormValue("discountedPrice", price);
              const profit = calculateProfit(price, costOfGoods);
              const margin = calculateMargin(profit, price);
              setFormValue("profit", profit);
              setFormValue("margin", margin);
            }
          }}
          size="sm"
        >
          On Sale
        </Switch>

        {isOnSale && (
          <div className="flex items-center gap-4">
            {/* Discount */}
            <Input
              label="Discount"
              {...register("saleValue", {
                onChange: handleDiscountChange,
                setValueAs: (value: any) => (isNaN(value) ? 0 : Number(value)),
              })}
              labelPlacement="outside"
              endContent={
                <Tabs
                  classNames={{ tabList: "bg-lightPrimary rounded-md" }}
                  aria-label="Sale Type"
                  size="sm"
                  selectedKey={saleType}
                  onSelectionChange={(key) => {
                    setFormValue("saleType", key as SaleTypeEnum);
                    handleDiscountChange();
                  }}
                >
                  <Tab key="PERCENT" title="%" />
                  <Tab key="RUPEE" title="₹" />
                </Tabs>
              }
              placeholder="12"
              errorMessage={errors.saleValue?.message}
              isInvalid={!!errors.saleValue}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
              }}
            />

            {/* Sale Price */}
            <Controller
              name="discountedPrice"
              control={control}
              render={({ field }) => (
                <NumberInput
                  disabled
                  hideStepper
                  label="Sale Price"
                  {...field}
                  value={watch("discountedPrice")}
                  labelPlacement="outside"
                  startContent={"₹"}
                  classNames={{
                    inputWrapper:
                      "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
                  }}
                />
              )}
            />
          </div>
        )}
      </div>

      {/* Cost, Profit, Margin */}
      <div className="grid grid-cols-2 md:grid-cols-none md:flex gap-4 items-center mt-2">
        <Controller
          name="costOfGoods"
          control={control}
          render={({ field: { onChange, value, ref } }) => (
            <NumberInput
              ref={ref}
              disabled={price <= 0}
              hideStepper
              isRequired
              label="Cost of Goods"
              onValueChange={(val) => {
                onChange(val);
                handleCostOfGoodsChange(val);
              }}
              value={value || 0}
              labelPlacement="outside"
              startContent="₹"
              errorMessage={errors?.costOfGoods?.message}
              isInvalid={!!errors?.costOfGoods}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
              }}
            />
          )}
        />

        <Controller
          name="profit"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              disabled
              label="Profit"
              labelPlacement="outside"
              startContent={"₹"}
              value={String((field.value || 0).toFixed(2))}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
              }}
            />
          )}
        />

        <Controller
          name="margin"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              disabled
              label="Margin"
              labelPlacement="outside"
              endContent="%"
              value={String((field.value || 0).toFixed(2))}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-2",
              }}
            />
          )}
        />
      </div>
    </div>
  );
};

export default PricingSection;
