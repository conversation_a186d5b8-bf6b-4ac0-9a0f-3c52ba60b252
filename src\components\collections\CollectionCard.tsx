import { But<PERSON> } from "@heroui/react";
import { BiEdit, BiTrash } from "react-icons/bi";

type CollectionCardType = {
	name: string;
	imgsrc: string;
	collectionId: string;
	handleCollectionDelete: (collectionId: string) => void;
	handleCollectionEdit: (collectionId: string, name: string, imgsrc: string) => void;
};
const CollectionCard = ({
	name = "",
	imgsrc = "",
	collectionId = "",
	handleCollectionDelete = () => {},
	handleCollectionEdit = () => {},
}: CollectionCardType) => {
	return (
		<>
			<div className="rounded-md card-div relative dark:bg-slate-700 overflow-hidden group">
				{/* Action buttons */}
				<div className="absolute top-2 right-3 flex gap-2 z-20">
					<Button
						endContent={<BiEdit className="text-small " />}
						variant="ghost"
						size="sm"
						isIconOnly
						color="primary"
						className="border-none bg-lightPrimary"
						radius="full"
						onPress={() => handleCollectionEdit(collectionId, name, imgsrc)}
					/>
					<Button
						endContent={<BiTrash className="text-small " />}
						variant="ghost"
						size="sm"
						isIconOnly
						color="primary"
						className="border-none bg-lightPrimary"
						radius="full"
						onPress={() => handleCollectionDelete(collectionId)}
					/>
				</div>

				{/* Full width image with hover zoom */}
				<div className="w-full h-[260px] overflow-hidden">
					<img
						src={imgsrc}
						alt={name}
						className="w-full h-full object-cover object-top transition-transform duration-300 ease-in-out group-hover:scale-110"
					/>
				</div>

				{/* Collection name overlay */}
				<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
					<h2 className="text-white font-medium text-lg">{name}</h2>
				</div>
			</div>
		</>
	);
};

export default CollectionCard;
