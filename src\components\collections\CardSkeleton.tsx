import { Card, Skeleton } from "@heroui/react";

const CardSkeleton = () => {
	return (
		<Card className="relative !h-[260px] card-div space-y-5" radius="lg">
			<Skeleton className=" w-1/3 mx-auto">
				<div className="h-[260px] rounded-lg bg-default-300" />
			</Skeleton>
			<div className="absolute top-0 right-3 flex gap-2 ">
				<Skeleton className="w-full rounded-full bg-default-400 ">
					<div className="h-8 w-8 rounded-full bg-default-200" />
				</Skeleton>
				<Skeleton className="w-full rounded-full bg-default-400 ">
					<div className="h-8 w-8 rounded-full bg-default-200" />
				</Skeleton>
			</div>
			<div className="absolute bottom-5 left-3">
				<Skeleton className="w-full rounded-lg bg-default-200">
					<div className="h-8 w-20 rounded-lg bg-default-200" />
				</Skeleton>
			</div>
		</Card>
	);
};

export default CardSkeleton;
