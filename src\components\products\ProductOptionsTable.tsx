import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  getKeyValue,
  NumberInput,
  Select,
  SelectItem,
  Input,
} from "@heroui/react";
import { useBoundStore } from "../../store/store";
import { ProductVariantsType } from "../../types/productType";
import { StatusTypes, StockStatus } from "../../types/commonTypes";
import { IoEyeOffOutline, IoEyeOutline } from "react-icons/io5";
import CustomSwitch from "../CustomSwitch";

const columns = [
  {
    key: "selectedOptions",
    label: "Variant",
  },
  {
    key: "priceDifference",
    label: "Price Difference(+/-)",
  },
  {
    key: "variantPrice",
    label: "Variant Price",
  },
  {
    key: "variantCostOfGoods",
    label: "Cost of Goods",
  },
  {
    key: "sku",
    label: "SKU",
  },
  {
    key: "stockStatus",
    label: "Status",
  },
  { key: "stockQuantity", label: "Store Inventory" },
  {
    key: "shippingWeight",
    label: "Shipping Weight",
  },
  { key: "visibility", label: "Visibility" },
];

const INITIAL_VISIBLE_COLUMNS = [
  "selectedOptions",
  "priceDifference",
  "variantPrice",
  "variantCostOfGoods",
  "sku",
  "status",
  "shippingWeight",
  "visibility",
];

export default function ProductOptionsTable() {
  const { variants, setVariantValue, trackInventory, setTrackInventory } =
    useBoundStore();
  const renderCell = (item: ProductVariantsType, columnKey: string) => {
    const cellValue = item[columnKey as keyof ProductVariantsType];
    switch (columnKey) {
      case "selectedOptions":
        return (
          <div className="text-nowrap whitespace-nowrap">
            {item.selectedOptions?.join(" | ")}
          </div>
        );
      case "priceDifference":
        return (
          <NumberInput
            hideStepper
            size="sm"
            aria-label="Price Difference"
            className="z-10"
            value={item.priceDifference}
            startContent={"₹"}
            placeholder="0"
            onValueChange={(value) =>
              setVariantValue(item._id ?? "", "priceDifference", value)
            }
            classNames={{
              inputWrapper:
                "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0 h-10",
            }}
          />
        );
      case "variantCostOfGoods":
        return (
          <NumberInput
            hideStepper
            size="sm"
            aria-label="Cost of Goods"
            className="z-10"
            value={item.variantCostOfGoods}
            startContent={"₹"}
            placeholder=""
            onValueChange={(value) =>
              setVariantValue(item._id ?? "", "variantCostOfGoods", value)
            }
            classNames={{
              inputWrapper:
                "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0 h-10",
            }}
          />
        );
      case "sku":
        return (
          <Input
            size="sm"
            aria-label="SKU"
            className="z-10"
            value={item.sku}
            placeholder=""
            onValueChange={(value) =>
              setVariantValue(item._id ?? "", "sku", value)
            }
            classNames={{
              inputWrapper:
                "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0 h-10 min-w-24",
            }}
          />
        );
      case "stockStatus":
        return (
          <Select
            placeholder="Select"
            classNames={{
              trigger: "rounded-md py-0 min-h-10 min-w-32 h-10",
            }}
            aria-label="Status"
            defaultSelectedKeys={item.stockStatus ? [item.stockStatus] : undefined}
            onSelectionChange={(value) => {
              setVariantValue(
                item._id ?? "",
                "stockStatus",
                value.currentKey as StockStatus
              );
            }}
          >
            <SelectItem key={StockStatus.IN_STOCK}>
              {StockStatus.IN_STOCK}
            </SelectItem>
            <SelectItem key={StockStatus.OUT_OF_STOCK}>{StockStatus.OUT_OF_STOCK}</SelectItem>
          </Select>
        );
      case "stockQuantity":
        return (
          <NumberInput
            hideStepper
            size="sm"
            aria-label="Store Inventory"
            className="z-10"
            value={item.stockQuantity}
            placeholder="0"
            onValueChange={(value) =>
              setVariantValue(item._id ?? "", "stockQuantity", value)
            }
            classNames={{
              inputWrapper:
                "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0 h-10",
            }}
          />
        );
      case "shippingWeight":
        return (
          <NumberInput
            hideStepper
            size="sm"
            aria-label="Shipping Weight"
            className="z-10"
            value={item.shippingWeight}
            endContent={"kg"}
            placeholder="0"
            onValueChange={(value) =>
              setVariantValue(item._id ?? "", "shippingWeight", value)
            }
            classNames={{
              inputWrapper:
                "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-1 h-10",
            }}
          />
        );
      case "visibility":
        return item.visibility ? (
          <IoEyeOutline
            className="text-textStandard text-xl hover:cursor-pointer"
            onClick={() => setVariantValue(item._id ?? "", "visibility", false)}
          />
        ) : (
          <IoEyeOffOutline
            className="text-textStandard text-xl hover:cursor-pointer"
            onClick={() => setVariantValue(item._id ?? "", "visibility", true)}
          />
        );
      default:
        if (typeof cellValue === "object" && Array.isArray(cellValue)) {
          return (
            <div>
              {cellValue.map((val, idx) => (
                <span key={idx}>{JSON.stringify(val)}</span>
              ))}
            </div>
          );
        }
        return typeof cellValue === "object"
          ? JSON.stringify(cellValue)
          : cellValue;
    }
  };

  const calculatedColumns = useMemo(() => {
    if (trackInventory) {
      return columns.filter((column) => column.key !== "stockStatus");
    } else {
      return columns.filter((column) => column.key !== "stockQuantity");
    }
  }, [trackInventory]);
  return (
    <div className="flex flex-col gap-3 ">
      <div className="flex w-full justify-end">
        <CustomSwitch
          label="Track Inventory"
          selected={trackInventory}
          onSelectionChange={setTrackInventory}
        />
      </div>
      <Table
        aria-label="Rows actions table example with dynamic content"
        // selectionBehavior={"toggle"}
        // selectionMode="multiple"
        // onRowAction={() => {
        //   return;
        // }}

        className="productOptionsTable gap-0 border-1 border-gray-200 dark:border-slate-600 rounded-lg"
        classNames={{
          base: "max-h-[600px] overflow-auto",
          table: "min-h-[100px]",
          wrapper: "scrollbar p-0 shadow-none rounded-none mt-0",
          thead: "!rounded-none",
          th: "bg-[#c3e5ff] dark:bg-slate-900 py-3 !rounded-none font-semibold text-primaryBlack text-sm",
          tr: "hover:bg-lightPrimary/20 dark:hover:bg-slate-600 h-16 border-b",
        }}
      >
        <TableHeader columns={calculatedColumns}>
          {(column) => (
            <TableColumn key={column.key}>{column.label}</TableColumn>
          )}
        </TableHeader>
        <TableBody items={variants}>
          {(item) => (
            <TableRow key={item._id}>
              {(columnKey) => (
                <TableCell>
                  {renderCell(item, columnKey as string) ?? null}
                </TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
