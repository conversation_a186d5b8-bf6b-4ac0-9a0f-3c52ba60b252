import React from "react";

interface CardWrapperProps {
  title?: string | null;
  endContent?: React.ReactNode;
  bodyClassName?: string | null;
  children: React.ReactNode;
}
const CardWrapper = ({
  title = null,
  endContent = null,
  bodyClassName = "p-5 md:p-6",
  children,
}: CardWrapperProps) => {
  const renderTitle = () => {
    if (title && endContent) {
      return (
        <div>
          <div className="flex w-full justify-between items-center px-6">
            <h3 className="w-full py-4">{title}</h3>
            <div>{endContent}</div>
          </div>
          <div className="h-[1px] w-full bg-gray-200 dark:bg-slate-700"></div>
        </div>
      );
    }
    if (title) {
      return (
        <>
          <h3 className="w-full px-6 py-4">{title}</h3>
          <div className="h-[1px] w-full bg-gray-200 dark:bg-slate-700"></div>
        </>
      );
    }

    return null;
  };
  return (
    <div
      className={`bg-white border dark:border-slate-700 rounded-md  w-full dark:bg-slate-900 h-fit`}
    >
      {renderTitle()}
      <div className={bodyClassName}>{children}</div>
    </div>
  );
};

export default CardWrapper;
