import { UseFormRegister } from "react-hook-form";
import { IconType } from "react-icons";
import { ProductSchema } from "../../types/productType";

interface CustomFileInputProps {
	register: UseFormRegister<ProductSchema>;
	Icon?: IconType;
	titleText?: string;
	accept?: string; // e.g., "image/*" or "video/*"
	maxFileSizeMB?: number; // max size for each file
	multiple?: boolean;
	name: "images" | "videos";
}

export const CustomFileInput = ({
	register,
	Icon,
	titleText,
	accept = "image/*",
	multiple = true,
	name = "images",
}: CustomFileInputProps) => {
	return (
		<div className="flex items-center justify-center w-full">
			<label
				htmlFor={`dropzone-${name}`}
				className="flex flex-col items-center justify-center w-full h-28 border border-primary border-dashed rounded-lg cursor-pointer bg-lightPrimary dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
			>
				<div className="flex flex-col items-center justify-center pt-5 pb-6">
					{Icon && <Icon className="text-primary text-3xl" />}
					<p className=" text-primary dark:text-gray-400">{titleText || "Upload File"}</p>
				</div>
				<input
					id={`dropzone-${name}`}
					type="file"
					multiple={multiple}
					accept={accept}
					className="hidden"
					{...register(name)}
				/>
			</label>
		</div>
	);
};

interface CustomFileInput2Props extends CustomFileInputProps {
	handleFileChange: (name: keyof ProductSchema, files: FileList) => void;
}
export const CustomFileInput2 = ({
	register,
	Icon,
	accept = "image/*",
	titleText,
	multiple = true,
	name = "images",
	handleFileChange,
}: CustomFileInput2Props) => {
	return (
		<div className="flex items-center justify-center w-full">
			<label
				htmlFor={`dropzone-${name}`}
				className="flex flex-col items-center justify-center w-full h-10 px-4 border border-primary rounded-lg cursor-pointer bg-lightPrimary dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 "
			>
				<div className="flex items-center justify-center text-xs p-2">
					{Icon && <Icon className="text-primary" />}
					<p className=" text-primary whitespace-nowrap dark:text-gray-400">
						{titleText || "Upload File"}
					</p>
				</div>
				<input
					id={`dropzone-${name}`}
					type="file"
					multiple={multiple}
					accept={accept}
					className="hidden"
					{...register(name, {
						onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
							handleFileChange(name, e.target.files);
						},
					})}
				/>
			</label>
		</div>
	);
};
