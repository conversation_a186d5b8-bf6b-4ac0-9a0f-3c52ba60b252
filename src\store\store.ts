import { create } from "zustand";
import { createUserSlice, UserSlice } from "./slices/userSlice";
import { persist } from "zustand/middleware";
import { productSlice, ProductSlice } from "./slices/productSlice";
import { InventorySlice, inventorySlice } from "./slices/inventorySlice";
import {
  createProductOptionsSlice,
  ProductOptions,
} from "./slices/productOptionsTable";
import { createOrdersSlice, OrderOption } from "./slices/ordersSlice";
import { paymentsSlice, PaymentsSlice } from "./slices/paymentsSlice";
import { customersSlice, CustomersSlice } from "./slices/customersSlice";
import { lookbookSlice, LookbookSlice } from "./slices/lookbookSlice";

export const useBoundStore = create<
  UserSlice &
    ProductSlice &
    InventorySlice &
    ProductOptions &
    OrderOption &
    PaymentsSlice &
    CustomersSlice &
    LookbookSlice
>()(
  persist(
    (...a) => ({
      ...createUserSlice(...a),
      ...productSlice(...a),
      ...inventorySlice(...a),
      ...createProductOptionsSlice(...a),
      ...createOrdersSlice(...a),
      ...paymentsSlice(...a),
      ...customersSlice(...a),
      ...lookbookSlice(...a),
    }),
    { name: "persist-data", partialize: (state) => ({ user: state.user }) }
  )
);
