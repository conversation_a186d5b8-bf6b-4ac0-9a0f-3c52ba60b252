import React from "react";
import {
	UseFormSetValue,
	UseFormWatch,
	FieldErrors,
	Control,
	Controller,
	useFieldArray,
} from "react-hook-form";
import { <PERSON><PERSON>, Card, CardBody, Textarea, Image } from "@heroui/react";
import { BiPlus, BiTrash, BiImageAdd, BiVideoPlus } from "react-icons/bi";
import { CiImageOn } from "react-icons/ci";
import { v4 as uuid } from "uuid";

import { LookBookFormData } from "../../validation/lookbookValidationSchema";
import VideoPreview from "../forms/VideoPreview";

interface LookBookStep2Props {
	errors: FieldErrors<LookBookFormData>;
	watch: UseFormWatch<LookBookFormData>;
	setFormValue: UseFormSetValue<LookBookFormData>;
	control: Control<LookBookFormData>;
}

const LookBookStep2: React.FC<LookBookStep2Props> = ({ errors, watch, setFormValue, control }) => {
	const { fields, append, remove } = useFieldArray({
		control,
		name: "sections",
	});

	const sections = watch("sections") || [];

	const addSection = () => {
		append({
			id: uuid(),
			imageUrl: "",
			videourl: "",
			paragraph: "",
		});
	};

	const removeSection = (index: number) => {
		if (fields.length > 1) {
			remove(index);
		}
	};

	const handleImageUpload = (index: number, file: File) => {
		const updatedSections = [...sections];
		updatedSections[index] = {
			...updatedSections[index],
			imageFile: file,
			imageUrl: URL.createObjectURL(file),
		};
		setFormValue("sections", updatedSections);
	};

	const handleVideoUpload = (index: number, file: File) => {
		const updatedSections = [...sections];
		updatedSections[index] = {
			...updatedSections[index],
			videoFile: file,
			videourl: URL.createObjectURL(file),
		};
		setFormValue("sections", updatedSections);
	};

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-xl font-semibold mb-4">Content Sections</h2>
				<p className="text-gray-600 dark:text-gray-400 mb-6">
					Add content sections to tell your story. Each section can have an image or video with
					accompanying text.
				</p>
			</div>

			<div className="space-y-6">
				{fields.map((field, index) => (
					<Card key={field.id} className="p-4">
						<CardBody className="space-y-4">
							<div className="flex justify-between items-center">
								<h3 className="text-lg font-medium">Section {index + 1}</h3>
								{fields.length > 1 && (
									<Button
										isIconOnly
										size="sm"
										color="danger"
										variant="light"
										onPress={() => removeSection(index)}
									>
										<BiTrash />
									</Button>
								)}
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{/* Media Upload Section */}
								<div className="space-y-4">
									<h4 className="font-medium">Media</h4>

									{/* Display current media */}
									{sections[index]?.imageUrl && (
										<div className="relative w-full h-40">
											<Image
												src={sections[index].imageUrl}
												alt={`Section ${index + 1} image`}
												className="w-full h-40 object-cover rounded-md"
											/>
										</div>
									)}

									{sections[index]?.videourl && !sections[index]?.imageUrl && (
										<div className="relative w-full h-40">
											<VideoPreview src={sections[index].videourl || ""} />
										</div>
									)}

									{/* Upload buttons */}
									<div className="flex gap-2">
										<label className="cursor-pointer">
											<input
												type="file"
												accept="image/*"
												className="hidden"
												onChange={(e) => {
													const file = e.target.files?.[0];
													if (file) {
														handleImageUpload(index, file);
													}
												}}
											/>
											<Button as="span" size="sm" variant="bordered" startContent={<BiImageAdd />}>
												{sections[index]?.imageUrl ? "Change Image" : "Add Image"}
											</Button>
										</label>

										<label className="cursor-pointer">
											<input
												type="file"
												accept="video/*"
												className="hidden"
												onChange={(e) => {
													const file = e.target.files?.[0];
													if (file) {
														handleVideoUpload(index, file);
													}
												}}
											/>
											<Button as="span" size="sm" variant="bordered" startContent={<BiVideoPlus />}>
												{sections[index]?.videourl ? "Change Video" : "Add Video"}
											</Button>
										</label>
									</div>

									{!sections[index]?.imageUrl && !sections[index]?.videourl && (
										<div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
											<CiImageOn className="mx-auto h-12 w-12 text-gray-400" />
											<p className="mt-2 text-sm text-gray-500">
												Add an image or video for this section
											</p>
										</div>
									)}
								</div>

								{/* Content Section */}
								<div className="space-y-4">
									<h4 className="font-medium">Content</h4>

									<Controller
										control={control}
										name={`sections.${index}.paragraph`}
										render={({ field }) => (
											<Textarea
												label="Section Content"
												placeholder="Write your section content here..."
												value={field.value || ""}
												onChange={field.onChange}
												minRows={8}
												maxRows={12}
												labelPlacement="outside"
												classNames={{
													inputWrapper: "rounded-md",
													label: "text-sm font-medium",
												}}
											/>
										)}
									/>
								</div>
							</div>
						</CardBody>
					</Card>
				))}
			</div>

			{/* Add Section Button */}
			<div className="flex justify-center">
				<Button color="primary" variant="bordered" startContent={<BiPlus />} onPress={addSection}>
					Add Another Section
				</Button>
			</div>

			{/* Validation Error */}
			{errors.sections && <div className="text-danger text-sm">{errors.sections.message}</div>}

			{/* Help Text */}
			<div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
				<h4 className="font-medium text-green-900 dark:text-green-100 mb-2">Section Tips:</h4>
				<ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
					<li>• Each section should tell part of your story</li>
					<li>• Use high-quality images or videos that complement your text</li>
					<li>• Keep your content engaging and concise</li>
					<li>• You can add as many sections as needed</li>
					<li>• At least one section is required</li>
				</ul>
			</div>
		</div>
	);
};

export default LookBookStep2;
