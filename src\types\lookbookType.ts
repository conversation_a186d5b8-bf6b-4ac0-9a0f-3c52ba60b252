import { StatusTypes } from "./commonTypes";

export interface AssetInputType {
	altText?: string | null;
	isFeatured?: boolean | null;
	path?: string | null;
	type?: "IMAGE" | "VIDEO" | null;
}

export interface SectionInputType {
	imageUrl?: string | null;
	paragraph?: string | null;
	videourl?: string | null;
}

export interface LookBookSchema {
	assets?: AssetInputType[];
	description?: string | null;
	name?: string | null;
	productIds?: string[] | null;
	sections?: SectionInputType[];
	status?: StatusTypes | null;
	totalPrice?: number | null;
	images?: FileList;
	videos?: FileList;
}

export interface LookBookAsset {
	_id: string;
	path: string;
	type: "IMAGE" | "VIDEO";
	isFeatured: boolean;
	altText: string;
	createdAt: string;
	updatedAt: string;
}

export interface LookBookSection {
	imageUrl?: string | null;
	paragraph?: string | null;
	videourl?: string | null;
}

export interface LookBookProduct {
	_id: string;
	name: string;
	price: number;
	assets?: LookBookAsset[];
}

export interface LookBookType {
	_id: string;
	name: string;
	description: string;
	totalPrice?: number;
	status: StatusTypes;
	assets: LookBookAsset[];
	sections?: LookBookSection[];
	productIds?: string[];
	products?: LookBookProduct[];
	isDeleted?: boolean;
	createdAt: string;
	updatedAt: string;
}

export interface GetLookBooksResponse {
	getLookBooks: {
		lookBooks: LookBookType[];
	};
}

export interface GetLookBookByIdResponse {
	getLookBookById: LookBookType;
}

// Form step types
export interface LookBookFormStep1 {
	name: string;
	description: string;
	totalPrice?: number;
	status: StatusTypes;
	images?: FileList;
	videos?: FileList;
}

export interface LookBookFormStep2 {
	sections: Array<{
		id: string;
		imageUrl?: string;
		videourl?: string;
		paragraph?: string;
		imageFile?: File;
		videoFile?: File;
	}>;
}

export interface LookBookFormStep3 {
	productIds: string[];
}

export interface LookBookFormData extends LookBookFormStep1, LookBookFormStep2, LookBookFormStep3 {
	assets?: AssetInputType[];
}
