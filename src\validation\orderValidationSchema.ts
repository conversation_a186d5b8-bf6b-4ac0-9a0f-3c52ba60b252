import * as z from "zod";
import { StockStatus, StatusTypes } from "../types/commonTypes";

export const orderValidationSchema = z.object({
	cart: z
		.array(
			z.object({
				asset: z
					.object({
						altText: z.string(),
						isFeatured: z.boolean(),
						path: z.string(),
						type: z.enum(["IMAGE", "VIDEO"]),
					})
					.optional()
					.nullable(),
				finalPrice: z.number().min(0, "Final price must be non-negative"),
				name: z.string().min(1, "Product name is required"),
				price: z.number().min(0, "Price must be non-negative"),
				productId: z.string().min(1, "Product ID is required"),
				qty: z.number().min(1, "Quantity must be at least 1"),
				categoryId: z.string().optional().nullable(),
				variantDetail: z
					.object({
						priceDifference: z.number().optional().nullable(),
						selectedOptions: z.array(z.string()),
						shippingWeight: z.number().optional().nullable(),
						variantPrice: z.number().min(0, "Variant price must be non-negative"),
						variantDetailId: z.string().optional().nullable(),
						variantCostOfGoods: z
							.number()
							.min(0, "Variant cost of goods must be non-negative")
							.optional()
							.nullable(),
						trackInventory: z.boolean().optional().nullable(),
						stockStatus: z.nativeEnum(StockStatus).optional(),
						stockQuantity: z.number().optional(),
						status: z.nativeEnum(StatusTypes).optional(),
						sku: z.string().optional().nullable(),
					})
					.optional()
					.nullable(),
			})
		)
		.min(1, "Add atleast 1 product"),

	userData: z.object({
		userId: z.string().optional(),
		firstName: z.string().min(1, "First name is required"),
		lastName: z.string().min(1, "Last name is required"),
		email: z.string().email("Invalid email").min(1, "Email is required"),
		phone: z.string().optional(),
		countryCode: z.string().optional().nullable(),
	}),
	customeDeliveryOptions: z
		.object({
			name: z.string().optional(),
			rate: z.number().optional(),
			schedule: z.boolean().optional(),
			date: z.string().optional(),
			fromTime: z.string().optional(),
			totime: z.string().optional(),
			pickup: z.boolean().optional(),
		})
		.optional()
		.refine(
			(val) => {
				if (!val?.schedule) return true;
				return !!val?.date && !!val?.fromTime && !!val?.totime;
			},
			{
				message: "Date and time range are required when scheduling",
				path: ["date"],
			}
		),
	customFee: z
		.array(
			z.object({
				name: z.string().min(1, "Fee name is required"),
				amount: z.number().min(0, "Rate must be non-negative"),
			})
		)
		.default([]),
	customDiscount: z
		.array(
			z.object({
				discountReason: z.string().min(1, "Discount name is required"),
				amount: z.number().min(0, "Rate must be non-negative"),
			})
		)
		.default([]),
	totalPrice: z.number().min(0, "Total price must be non-negative"),
	totalDiscount: z.number().min(0, "Total discount must be non-negative").optional(),
	taxAmount: z.number().min(0, "Tax amount must be non-negative").optional(),
	tagIds: z.array(z.string()).optional(),
	shippingAmount: z.number().min(0, "Shipping amount must be non-negative").optional(),
	shippingAddressId: z.string().optional(),
	discountedPrice: z.number().min(0, "Discounted price must be non-negative").optional(),
	shippingAddress: z
		.object({
			addressType: z.enum(["WORK", "HOME", "BILLING", "SHIPPING", "OTHER"]),
			flat: z.number(),
			addressline1: z.string().min(1, "Address line 1 is required"),
			addressline2: z.string().optional(),
			landmark: z.string().optional(),
			//countryCode: z.string().optional(),
			phone: z.string().optional(),
			city: z.string().min(1, "City is required"),
			country: z.string().min(1, "Country is required"),
			states: z.string().min(1, "State is required"),
			pincode: z.string().min(1, "Pincode is required"),
			primary: z.boolean().optional(),
		})
		.optional(),
	billingAddressId: z.string().optional(),
	billingAddress: z
		.object({
			addressType: z.enum(["WORK", "HOME", "BILLING", "SHIPPING", "OTHER"]),
			flat: z.number().optional(),
			addressline1: z.string().min(1, "Address line 1 is required"),
			addressline2: z.string().optional(),
			landmark: z.string().optional(),
			countryCode: z.string().optional(),
			phone: z.string().optional(),
			city: z.string().min(1, "City is required"),
			country: z.string().min(1, "Country is required"),
			states: z.string().min(1, "State is required"),
			pincode: z.string().min(1, "Pincode is required"),
			primary: z.boolean().optional(),
		})
		.optional(),
	seenByHuman: z.boolean().optional(),
	orderPrice: z.number().min(0, "Order price must be non-negative"),
	itemcount: z.number().min(0, "Item count must be non-negative"),
	ipAddress: z.string().optional(),
	paymentStatus: z.enum(["PAID", "UNPAID", "PENDING", "REFUNDED"]).optional(),
	currency: z.string().optional().default("INR"),
	deliveryMethod: z.enum(["CUSTOM", "NO_CHARGE"]).optional(),
	location: z.object({
		useLocation: z.boolean(),
		address: z.string().refine(
			(val) => {
				// If useLocation is true, address should not be empty
				return !val || val.trim().length > 0;
			},
			{
				message: "Address is required when location is enabled",
			}
		),
	}),
});
