import { useEffect, useState } from "react";
import TopContent from "../components/products/TopContent";
import Card<PERSON>rapper from "../components/CardWrapper";
import {
	addToast,
	BreadcrumbItem,
	Breadcrumbs,
	Button,
	Form,
	Input,
	Listbox,
	ListboxItem,
	Spinner,
	Switch,
	useDisclosure,
} from "@heroui/react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";
import { Ribbon, SEO, Speaker } from "../assets/SVGS";
import { Controller, SubmitHandler, useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CategoriesType, ProductOptions, ProductSchema, ProductVariantsType } from "../types/productType";
import { useBoundStore } from "../store/store";
import { BiPlus } from "react-icons/bi";
import { useNavigate, useParams } from "react-router-dom";
import ModalComponent from "../components/ModalComponent";
import AddProductOptions from "../components/products/AddProductOptions";
import { GET_COLLECTIONS } from "../graphql/collections";
import { useMutation, useQuery } from "@apollo/client";
import { CREATE_PRODUCT, GET_PRODUCT_BY_ID, UPDATE_PRODUCT } from "../graphql/products";
import ProductOptionsTable from "../components/products/ProductOptionsTable";
import ManageVariantsTable from "../components/products/ManageVariantsTable";
import { StatusTypes, StockStatus } from "../types/commonTypes";
import { v4 as uuid } from "uuid";
import { productTypes } from "../helpers/constants";
import { PUT_FILE } from "../graphql/files";
import CustomTexts from "../components/products/CustomTexts";
import PricingSection from "../components/products/PricingSection";
import ProductVariants from "../components/products/ProductVariants";
import { productValidationSchema } from "../validation/productValidationSchema";
import { formateFolderName } from "../helpers/formatters";

type PropsTypes = {
	method?: string;
};

const AddEditProducts = ({ method }: PropsTypes) => {
	const { id } = useParams();
	const navigate = useNavigate();
	const [input, setInput] = useState("");
	const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
	const {
		isOpen: isProductOptionsOpen,
		onOpen: onProductOptionsOpen,
		onOpenChange: onProductOptionsOpenChange,
		onClose: onProductOptionsClose,
	} = useDisclosure();
	// const {
	//   isOpen: isConfirmationOpen,
	//   onOpen: onConfirmationOpen,
	//   onClose: onConfirmationClose,
	//   onOpenChange: onConfirmationOpenChange,
	// } = useDisclosure();
	const {
		customTextFields,
		addCustomTextField,
		addAnotherCustomTextField,
		removeCustomTextField,
		productOptions,
		setProductOptions,
		resetProductOptions,
		addVariant,
		variants,
		resetVariants,
		isManageVariants,
		trackInventory,
		allowCustomText,
		setCustomTextField,
		setManageVariants,
		productOptionIndex,
		setProductOptionIndex,
	} = useBoundStore();

	const { data, loading, error } = useQuery(GET_COLLECTIONS);
	const {
		data: productIdData,
		loading: productIdLoading,
		error: productIdError,
	} = useQuery(GET_PRODUCT_BY_ID, {
		skip: method === "POST",
		variables: { getProductByIdId: id },
	});
	const [createProduct, { loading: createProductLoading }] = useMutation(CREATE_PRODUCT);
	const [updateProduct, { loading: updateProductLoading }] = useMutation(UPDATE_PRODUCT);

	const [getSignedUrlToPutObject] = useMutation(PUT_FILE);

	const {
		register,
		formState: { errors },
		watch,
		getValues,
		setValue: setFormValue,
		resetField,
		reset,
		control,
		handleSubmit,
	} = useForm<ProductSchema>({
		resolver: zodResolver(productValidationSchema),
		defaultValues: {
			assets: [],
			name: "",
			ribbon: {
				name: "",
				ribbonId: "",
			},
			description: "",
			isOnSale: false,
			allowCustomText: false,
			categoryIds: [],
			productOptions: [],
			trackInventory: true,
			status: StatusTypes.PUBLISHED,
			saleType: "PERCENT",
			saleValue: null,
			discountedPrice: null,
			profit: 0,
			margin: 100,
			images: new DataTransfer().files,
			videos: new DataTransfer().files,
			customTexts: [],
		},
	});

	const { append, remove, update } = useFieldArray({
		control,
		name: "productOptions",
	});
	const { remove: customTextRemove } = useFieldArray({
		control,
		name: "customTexts",
	});

	useEffect(() => {
		if (productIdData) {
			const productOptionList = productIdData.getProductById.productOptions || []
			const allProductOptions = productOptionList.map(({ __typename, ...rest }) => rest);
			reset({
				assets: productIdData.getProductById.assets || [],
				name: productIdData.getProductById.name || "",
				ribbon: {
					name: productIdData.getProductById.ribbon?.name || "",
					ribbonId: productIdData.getProductById.ribbon?.ribbonId || "",
				},
				description: productIdData.getProductById.description || "",
				isOnSale: productIdData.getProductById.isOnSale || false,
				allowCustomText: productIdData.getProductById.allowCustomText || false,
				categoryIds: productIdData.getProductById.categoryIds || [],
				productOptions: allProductOptions,
				trackInventory: productIdData.getProductById.trackInventory || true,
				status: productIdData.getProductById.status || StatusTypes.DRAFT,
				saleType: productIdData.getProductById.saleType || "PERCENT",
				profit: productIdData.getProductById.profit || 0,
				margin: productIdData.getProductById.margin || 100,
				price: productIdData.getProductById.price,
				saleValue: productIdData.getProductById.saleValue,
				discountedPrice: productIdData.getProductById.discountedPrice,
				costOfGoods: productIdData.getProductById.costOfGoods,
				customTexts: productIdData.getProductById.customTexts,
				images: new DataTransfer().files,
				videos: new DataTransfer().files,
			});
			const variants = productIdData.getProductById.variants || [];
			const newVariants = variants?.map(({ __typename, ...rest }) => { return rest });
			addVariant(newVariants);
			setManageVariants(productIdData.getProductById.manageVariantQtyAndPrice || false);
			setCustomTextField(productIdData.getProductById.customTexts);
		}
	}, [productIdData]);
	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter" && input.trim()) {
			const newChoices = [...productOptions.choices, { name: input.trim(), images: [] }];
			setProductOptions({ ...productOptions, choices: newChoices });
			setInput("");
		}
	};
	const removeTag = (choiceIndex: number) => {
		const newChoices = productOptions.choices.filter((_, index) => index !== choiceIndex);
		setProductOptions({ ...productOptions, choices: newChoices });
	};
	const handleAddOption = () => {
		if (productOptionIndex !== null) {
			update(productOptionIndex, productOptions);
		} else {
			append(productOptions);
		}
		resetProductOptions();
		// Generate Product Options
		const updatedVariants = generateProductOptions();
		addVariant(updatedVariants);
		setProductOptionIndex(null);
		onClose(); // Close the modal
	};

	const handleTabChange = (newValue: string) => {
		setProductOptions({
			...productOptions,
			showInProductPageAs: newValue,
			choices: [],
		});
	};

	const handleColorChange = (color: any, choiceIndex: number) => {
		const newChoices = productOptions.choices.map((choice, index) =>
			index === choiceIndex ? { ...choice, name: color.hex } : choice
		);
		setProductOptions({ ...productOptions, choices: newChoices });
	};

	const handleProductOptionsAdd = () => {
		onProductOptionsClose();
	};

	const generateProductOptions = () => {
		// Generate all combinations of product choices
		const productOptions = getValues("productOptions") || [];
		// Extract choices as arrays of names
		const allProductVariants = productOptions
			?.map((option) => option.choices.map((choice) => choice.name)) // Extract only names
			.reduce(
				(acc: any, choices) => {
					return acc.flatMap((prev: any) => choices.map((choice) => [...prev, choice]));
				},
				[[]]
			); // Start with an empty array

		// Format the result as required
		const formattedVariants = allProductVariants.map((variant: ProductVariantsType) => ({
			selectedOptions: variant,
			visibility: true,
			variantPrice: !isNaN(getValues("discountedPrice")) ? getValues("discountedPrice") : 0.0,
			status: watch("status") || StatusTypes.PUBLISHED,
			stockStatus: StockStatus.IN_STOCK,
			_id: uuid(),
		}));
		return formattedVariants;
	};
	const handleAddProductOptionsModal = () => {
		if (!isManageVariants) {
			if (!isProductOptionsOpen) {
				onProductOptionsOpen();
			} else {
				onProductOptionsClose();
			}
		}
		setManageVariants(!isManageVariants);
	};

	const onSubmit: SubmitHandler<ProductSchema> = async (data) => {
		const { allowCustomText, images, videos, assets, ribbon, isOnSale, saleValue, price, discountedPrice, ...rest } = data;
		const folderName = formateFolderName(data.name);
		const isAllowCustomText = (data?.customTexts?.length ?? 0) > 0;
		const newVariant = variants && variants.length > 0 ? variants : generateProductOptions();
		const finalVariant = newVariant.map(
			({ _id, ...variantRest }: { _id: string; [key: string]: any }) => variantRest
		);

		// Convert FileList to Array
		const filesArray = [...(images ?? []), ...(videos ?? [])];

		// Prepare existing assets (from server) - these should be preserved
		const existingAssets = assets || [];

		// Only upload new files if there are any
		let newlyUploadedAssets: Array<{
			altText: string;
			isFeatured: boolean;
			path: string;
			type: "IMAGE" | "VIDEO";
		}> = [];

		if (filesArray.length > 0) {
			// Prepare filesData payload for new files
			const filesData = filesArray.map((file) => ({
				fileName: file.name,
				extension: file.name.split(".").pop(),
				contentType: file.type,
				fileKey: `products/${folderName}-${Date.now()}/${file.name}`,
			}));

			const { data: signedUrlsResponse } = await getSignedUrlToPutObject({
				variables: { filesData },
			});

			if (!signedUrlsResponse || !signedUrlsResponse.getSignedUrlToPutObject) {
				throw new Error("Failed to get signed URLs");
			}

			const uploadedFiles = await Promise.all(
				signedUrlsResponse.getSignedUrlToPutObject.map(
					async (fileInfo: { url: string; cdnUrl: string }, index: number) => {
						try {
							const response = await fetch(fileInfo.url, {
								method: "PUT",
								body: filesArray[index],
								headers: { "Content-Type": filesArray[index].type },
							});

							if (!response.ok) throw new Error(`Upload failed for ${filesArray[index].name}`);
							return {
								altText: filesArray[index].name,
								isFeatured: false, // Adjust based on requirement
								path: fileInfo.cdnUrl, // S3 file path
								type: filesArray[index].type.includes("image") ? "IMAGE" : "VIDEO",
							};
						} catch (error) {
							console.error(`Error uploading ${filesArray[index].name}:`, error);
							return null; // Return null for failed uploads
						}
					}
				)
			);
			// Filter out any failed uploads
			newlyUploadedAssets = uploadedFiles.filter(Boolean);
		}

		// Combine existing assets with newly uploaded assets
		const allAssets = [...existingAssets, ...newlyUploadedAssets];

		// Ensure we have at least some assets (either existing or new)
		if (allAssets.length === 0 && method === "POST") {
			throw new Error("At least one image or video is required");
		}

		try {
			const handleResponse = ({
				title,
				color = "primary",
				redirectUrl,
			}: {
				title: string;
				color: string;
				redirectUrl?: string;
			}) => {
				addToast({
					title,
					shouldShowTimeoutProgress: true,
					color: color as
						| "primary"
						| "default"
						| "foreground"
						| "secondary"
						| "success"
						| "warning"
						| "danger",
				});
				if (redirectUrl) {
					setTimeout(() => navigate(redirectUrl), 1000);
				}
			};

			const input = {
				assets: allAssets, // Use combined assets (existing + new)
				productType: productTypes[0],
				allowCustomText: isAllowCustomText,
				manageVariantQtyAndPrice: isManageVariants,
				variants: finalVariant,
				ribbon: ribbon ? { name: ribbon.name } : null,
				price,
				isOnSale,
				saleValue,
				discountedPrice: isOnSale && saleValue > 0 ? discountedPrice : price,
				...rest,
			};

			if (method === "POST") {
				await createProduct({ variables: { input } })
					.then((res) => {
						if (res.data) {
							handleResponse({
								title: "Product added successfully",
								redirectUrl: "/admin/store/products",
								color: "success",
							});
						}
					})
					.catch(() => handleResponse({ title: "Error adding product", color: "danger" }));
			} else {
				await updateProduct({ variables: { input, productId: id } })
					.then((res) => {
						if (res.data) {
							handleResponse({
								title: "Product updated successfully",
								redirectUrl: "/admin/store/products",
								color: "success",
							});
						}
					})
					.catch(() => handleResponse({ title: "Error updating product", color: "danger" }));
			}
		} catch (error) {
			console.error("Error in file upload or product update:", error);
			addToast({
				title: "Error uploading files or adding product",
				color: "danger",
			});
		}
	};

	const handleSaveClick = async () => {
		await handleSubmit(onSubmit)();
	};

	// const handleProductOptionsModalClose = () => {
	//   // resetVariants();
	//   onProductOptionsClose();
	// };

	if (!productIdError && productIdLoading) {
		return (
			<div className="flex justify-center items-center h-screen">
				<Spinner className="h-20 w-20" />
			</div>
		);
	}
	const handleCancel = () => {
		navigate("/admin/store/products");
	};

	return (
		<>
			<div className="grid grid-cols-1 py-2 md:grid-cols-2 justify-end items-center border-b dark:border-slate-700 mb-5 ml-1 sticky top-[3.15rem] bg-white dark:bg-slate-900 left-0 z-30 ">
				<div className="px-5">
					<Breadcrumbs>
						<BreadcrumbItem>Home</BreadcrumbItem>
						<BreadcrumbItem>Products</BreadcrumbItem>
					</Breadcrumbs>
				</div>
				<div className="flex gap-x-4 w-full justify-end">
					<Button size="sm" radius="full" color="primary" variant="ghost" onPress={handleCancel}>
						Cancel
					</Button>
					<Button
						size="sm"
						radius="full"
						color="primary"
						isLoading={createProductLoading || updateProductLoading}
						onPress={handleSaveClick}
					>
						Save
					</Button>
				</div>
			</div>
			<ModalComponent
				modalHeader={"Add Product Option"}
				isOpen={isOpen}
				onPress={handleAddOption}
				onOpenChange={onOpenChange}
				subheading={
					"You'll be able to manage pricing and inventory for this product option later on."
				}
				size="xl"
			>
				<AddProductOptions
					errors={errors}
					handleColorChange={handleColorChange}
					handleKeyDown={handleKeyDown}
					handleTabChange={handleTabChange}
					input={input}
					setInput={setInput}
					productOptions={productOptions}
					removeTag={removeTag}
					setProductOptions={setProductOptions}
				/>
			</ModalComponent>
			<ModalComponent
				isOpen={isProductOptionsOpen}
				onOpenChange={handleAddProductOptionsModal}
				modalHeader={"Product Variants"}
				subheading={
					"Based on your product options, these are the different versions of your product that customers can buy. "
				}
				saveButtonText={"Apply"}
				onModalClose={onProductOptionsClose}
				onPress={handleProductOptionsAdd}
				size="5xl"
				isDismissable={false}
			>
				<ProductOptionsTable />
			</ModalComponent>
			{/* <ModalComponent
        modalHeader={"Reset values for variant"}
        isOpen={isConfirmationOpen}
        onOpenChange={onConfirmationOpenChange}
        onPress={handleConfirmationVariantChange}
      >
        <div className="flex flex-col items-center gap-y-2">
          <FiAlertTriangle className="text-3xl" />
          <h5>
            Any changes you've made will reset to original values. Do you want
            to continue?
          </h5>
        </div>
      </ModalComponent> */}
			<Form onSubmit={handleSubmit(onSubmit)}>
				<div className="md:p-4 grid grid-cols-1 w-full gap-7 md:grid-cols-12 ">
					<div className="flex flex-col gap-y-7 md:col-span-8">
						<CardWrapper title="Images & Videos">
							<TopContent
								setFormValue={setFormValue}
								watch={watch}
								errors={errors}
								register={register}
							/>
						</CardWrapper>

						<CardWrapper title={"Product Info"}>
							<div className="flex flex-col gap-y-7">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<Controller
										control={control}
										name="name"
										render={({ field }) => (
											<Input
												isRequired
												label="Name"
												{...field}
												labelPlacement="outside"
												errorMessage={errors.name?.message}
												isInvalid={!!errors.name}
												placeholder="Add a product name"
												classNames={{
													inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md z-10",
												}}
											/>
										)}
									/>

									<Controller
										control={control}
										name="ribbon.name"
										render={({ field }) => (
											<Input
												label="Ribbon"
												{...field}
												labelPlacement="outside"
												errorMessage={errors.ribbon?.name?.message}
												isInvalid={!!errors.ribbon?.name}
												placeholder="New Launch, On Sale, etc"
												classNames={{
													inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md",
												}}
											/>
										)}
									/>
								</div>
								<div>
									<p className={`mb-1.5 text-sm ${errors?.description && `text-red-500`}`}>
										Description <sup className="text-sm">*</sup>
									</p>
									<ReactQuill
										theme="snow"
										value={getValues("description")}
										onChange={(e: string) => setFormValue("description", e)}
										placeholder="Enter product description..."
										style={{ height: "150px", marginBottom: "50px" }}
									/>
									<small className="text-red-500"> {errors.description?.message}</small>
								</div>
								{/* <div>
                  <p className="mb-1.5">Delivery</p>
                  <ReactQuill theme="snow" value={value} onChange={setValue} />
                </div>
                <div>
                  <p className="mb-1.5">Returns</p>
                  <ReactQuill theme="snow" value={value} onChange={setValue} />
                </div> */}
							</div>
						</CardWrapper>
						<CardWrapper title={"Pricing"}>
							<PricingSection
								control={control}
								errors={errors}
								getValues={getValues}
								register={register}
								resetField={resetField}
								setFormValue={setFormValue}
								watch={watch}
							/>
						</CardWrapper>
						<CardWrapper title="Product options">
							<ProductVariants
								getValues={getValues}
								handleAddProductOptionsModal={handleAddProductOptionsModal}
								isManageVariants={isManageVariants}
								onOpen={onOpen}
								remove={remove}
							/>
						</CardWrapper>
						{isManageVariants && (
							<CardWrapper
								title="Manage variants"
								bodyClassName="p-0 md:p-0"
								endContent={
									<Button
										color="primary"
										className="border mt-2"
										onPress={onProductOptionsOpen}
										size="sm"
										variant="ghost"
										radius="full"
									>
										Edit
									</Button>
								}
							>
								<ManageVariantsTable />
							</CardWrapper>
						)}
						<CardWrapper title={"Custom Text"}>
							<p className="mb-4">
								Allow customers to personalize this product with a custom text field.
							</p>
							{customTextFields.length === 0 ? (
								<Button
									color="primary"
									className="border mt-2"
									onPress={addCustomTextField}
									size="sm"
									variant="ghost"
									radius="full"
								>
									Add Custom Text Field
								</Button>
							) : (
								<>
									{customTextFields.map((field, index) => (
										<CustomTexts
											control={control}
											index={index}
											customTextRemove={customTextRemove}
											errors={errors}
											removeCustomTextField={removeCustomTextField}
											key={index}
											setFormValue={setFormValue}
										/>
									))}
									<Button
										color="primary"
										startContent={<BiPlus />}
										className="mt-2 border"
										onPress={addAnotherCustomTextField}
										size="sm"
										variant="bordered"
										radius="full"
									>
										Add Another Field
									</Button>
								</>
							)}
						</CardWrapper>
					</div>
					<div className="flex flex-col gap-y-7 md:col-span-4">
						<CardWrapper>
							<div className="w-full flex items-center gap-x-4 h-fit">
								<Switch
									isSelected={watch("status") === StatusTypes.PUBLISHED}
									size="sm"
									onValueChange={(isSelected) => {
										setFormValue("status", isSelected ? StatusTypes.PUBLISHED : StatusTypes.DRAFT);
									}}
								>
									&nbsp;
								</Switch>
								<p>Show in online store</p>
							</div>
						</CardWrapper>
						<CardWrapper title="Categories">
							<div className="w-full ">
								<Controller
									name="categoryIds"
									control={control}
									render={({ field }) => (
										<Listbox
											// isVirtualized
											className="max-w-xs h-fit"
											classNames={{
												list: "listbox",
												base: "max-h-[400px] h-fit overflow-y-scroll scrollbar",
											}}
											variant="flat"
											selectionMode="multiple"
											selectedKeys={field.value}
											onSelectionChange={(selectedKeys) => {
												setFormValue("categoryIds", Array.from(selectedKeys as Set<string>));
											}}
											aria-label="Categories"
										>
											{data?.getCategories?.categories?.map((category: CategoriesType) => (
												<ListboxItem key={category._id} textValue={category._id} id={category._id}>
													{category.name}
												</ListboxItem>
											))}
										</Listbox>
									)}
								/>
							</div>
							<small className="text-red-500">{errors.categoryIds?.message} </small>
						</CardWrapper>
						<CardWrapper title="Marketing & SEO">
							<div className="flex flex-col gap-y-2">
								<button className="flex items-center gap-x-4 bg-transparent outline-none active:outline-none hover:text-primary">
									<Ribbon />
									<span>Create Coupon</span>
								</button>
								<button className="flex items-center gap-x-4 bg-transparent outline-none active:outline-none hover:text-primary">
									<Speaker />
									<span>Promote this product</span>
								</button>
								<button className="flex items-center gap-x-4 bg-transparent outline-none active:outline-none hover:text-primary">
									<SEO />
									<span>Edit SEO Settings</span>
								</button>
							</div>
						</CardWrapper>
					</div>
				</div>
			</Form>
		</>
	);
};

export default AddEditProducts;
