import {
	Button,
	DropdownTrigger,
	Dropdown,
	DropdownMenu,
	DropdownItem,
	Image,
	addToast,
	Chip,
	Select,
	SelectItem,
} from "@heroui/react";

import { useCallback, useMemo, useRef } from "react";
import { BiExport, BiPlus } from "react-icons/bi";
import { LuPencil } from "react-icons/lu";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import { useLookbookModals } from "../hooks/useLookbookModals";
import { RxDividerVertical } from "react-icons/rx";
import { CgTrashEmpty } from "react-icons/cg";
import TableComponent from "../components/Table";
import { CiExport, CiImageOn, CiImport } from "react-icons/ci";
import TopHeader from "../components/table/TopHeader";
import TopDrawer from "../components/TopDrawer";
import { IoChevronDown, IoEyeOutline, IoTrash } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import { CSVLink } from "react-csv";
import {
	GET_LOOKBOOKS,
	DELETE_LOOKBOOK,
	BULK_DELETE_LOOKBOOKS,
	UPDATE_LOOKBOOK,
} from "../graphql/lookbook";
import { useMutation, useQuery } from "@apollo/client";
import { useBoundStore } from "../store/store";
import ModalComponent from "../components/ModalComponent";
import { FiAlertTriangle } from "react-icons/fi";
import { useMutationHandler } from "../hooks/useMutationStatusHandler";
import { useInfiniteQueryScroll } from "../hooks/useInfiniteQueryScroll";
import { LookBookType } from "../types/lookbookType";
import { StatusTypes } from "../types/commonTypes";

export const columns = [
	{ name: "ID", uid: "_id", sortable: false },
	{ name: "Name", uid: "name", sortable: false },
	{ name: "Status", uid: "status", sortable: false },
	{ name: "Total Price", uid: "totalPrice", sortable: false },
	{ name: "Products", uid: "products", sortable: false },
	{ name: "Sections", uid: "sections", sortable: false },
	{ name: "ACTIONS", uid: "actions" },
];

const statusColorMap = {
	[StatusTypes.PUBLISHED]: "success",
	[StatusTypes.DRAFT]: "warning",
	[StatusTypes.ARCHIVED]: "default",
	[StatusTypes.SCHEDULED]: "primary",
} as const;

const LookBook = () => {
	const navigate = useNavigate();
	const {
		selectedLookbookKeys,
		setSelectedLookbookKeys,
		deleteLookbookId,
		setDeleteLookbookId,
		lookbookVisibleColumns,
		setLookbookVisibleColumns,
		lookbookFilterText,
		setLookbookFilterText,
		//lookbookFilters,
		//setLookbookFilters,
	} = useBoundStore();

	// Use the custom hook to manage all modal states
	const { mainModal, confirmationModal, bulkDeleteModal } = useLookbookModals();

	const { data, loading, fetchMore, refetch, error } = useQuery(GET_LOOKBOOKS, {
		variables: { limit: 10, offset: 0, filter: { search: lookbookFilterText } },
		fetchPolicy: "cache-and-network",
		notifyOnNetworkStatusChange: true,
	});

	// used to delete the lookbook from the table delete button click
	const [deleteLookbook, { data: deleteData, loading: deleteLoading, error: deleteError }] =
		useMutation(DELETE_LOOKBOOK, {
			refetchQueries: [{ query: GET_LOOKBOOKS, variables: { limit: 10, offset: 0 } }],
			awaitRefetchQueries: true,
		});

	// used to bulk delete lookbooks
	const [
		bulkDeleteLookbooks,
		{ loading: bulkDeleteLoading, error: bulkDeleteError, data: bulkDeleteData },
	] = useMutation(BULK_DELETE_LOOKBOOKS, {
		refetchQueries: [{ query: GET_LOOKBOOKS, variables: { limit: 10, offset: 0 } }],
		awaitRefetchQueries: true,
	});

	// used to update lookbook status
	const [
		updateLookBook,
		{ loading: updateStatusLoading, error: updateStatusError, data: updateStatusData },
	] = useMutation(UPDATE_LOOKBOOK, {
		refetchQueries: ["getLookBooks"],
		awaitRefetchQueries: true,
	});

	useMutationHandler({
		data: deleteData,
		loading: deleteLoading,
		error: deleteError,
		successMessage: "Lookbook deleted successfully",
		onClose: confirmationModal.onClose,
	});

	useMutationHandler({
		data: bulkDeleteData,
		loading: bulkDeleteLoading,
		error: bulkDeleteError,
		successMessage: "Lookbooks deleted successfully",
		onClose: bulkDeleteModal.onClose,
	});

	useMutationHandler({
		data: updateStatusData,
		loading: updateStatusLoading,
		error: updateStatusError,
		successMessage: "Lookbook status updated successfully",
	});

	// Get totalCount for display
	const totalCount = data?.getLookBooks?.totalCount || 0;

	// Use infinite scroll hook like in Products page
	const scrollContainerRef = useRef<HTMLDivElement | null>(null);
	const { hasMore } = useInfiniteQueryScroll({
		items: data?.getLookBooks?.lookBooks || [],
		totalCount,
		loading,
		fetchMore,
		scrollContainerRef,
	});

	// Determine if this is initial loading (no items) vs infinite scroll loading (has items)
	const isInitialLoading = loading && (data?.getLookBooks?.lookBooks || []).length === 0;

	// Handle status change
	const handleStatusChange = (lookbookId: string, status: StatusTypes) => {
		updateLookBook({
			variables: {
				updateLookBookId: lookbookId,
				input: {
					status,
				},
			},
		});
	};

	// used for rendering the cell data in the table
	const renderCell = useCallback(
		(lookBook: LookBookType, columnKey: string) => {
			const cellValue = lookBook[columnKey as keyof LookBookType];
			switch (columnKey) {
				case "name":
					return (
						<div className="flex items-center gap-3">
							{lookBook.assets && lookBook.assets.length > 0 ? (
								<Image
									src={lookBook.assets[0].path}
									alt={lookBook.assets[0].altText}
									className="w-12 h-12 object-cover rounded"
								/>
							) : (
								<div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
									<CiImageOn className="text-gray-400" />
								</div>
							)}
							<div>
								<div className="font-medium">{lookBook.name}</div>
								<div className="text-sm text-gray-500 truncate max-w-xs">
									{lookBook.productIds.length > 0
										? `${lookBook.productIds.length} products`
										: "No products"}
								</div>
							</div>
						</div>
					);
				case "status":
					return (
						<Select
							placeholder="Select"
							label=""
							classNames={{
								trigger: "w-40 rounded-md py-0 min-h-8 h-8",
							}}
							aria-label="Visibility"
							defaultSelectedKeys={[lookBook.status ? lookBook.status : StatusTypes.DRAFT]}
							onSelectionChange={(value) => {
								handleStatusChange(lookBook._id, value.currentKey as StatusTypes);
							}}
						>
							<SelectItem key={StatusTypes.PUBLISHED}>Published</SelectItem>
							<SelectItem key={StatusTypes.DRAFT}>Draft</SelectItem>
							<SelectItem key={StatusTypes.ARCHIVED}>Archived</SelectItem>
						</Select>
					);
				case "totalPrice":
					return lookBook.totalPrice ? `₹${lookBook.totalPrice.toFixed(2)}` : "-";
				case "products":
					return `${lookBook.productIds?.length || 0} products`;
				case "sections":
					return `${lookBook.sections?.length || 0} sections`;
				case "actions":
					return (
						<div className="relative flex items-center justify-center gap-3">
							<Button
								isIconOnly
								size="sm"
								variant="ghost"
								color="primary"
								className="bg-lightPrimary dark:bg-gray-700 border-none hover:bg-primary group rounded-full p-0 min-w-0 h-auto w-auto"
								onPress={() => navigate(`/admin/store/edit-lookbook/${lookBook._id}`)}
							>
								<LuPencil className="text-primary group-hover:text-white m-1.5" />
							</Button>
							<Button
								isIconOnly
								size="sm"
								variant="ghost"
								color="primary"
								className="bg-lightPrimary dark:bg-gray-700 border-none hover:bg-primary group rounded-full p-0 min-w-0 h-auto w-auto"
								onPress={() => {
									setDeleteLookbookId(lookBook._id);
									confirmationModal.onOpen();
								}}
							>
								<CgTrashEmpty className="text-primary text-xl group-hover:text-white m-1.5" />
							</Button>
						</div>
					);
				default:
					if (typeof cellValue === "object" && Array.isArray(cellValue)) {
						return (
							<div>
								{cellValue.map((val: unknown, idx: number) => (
									<span key={idx}>{JSON.stringify(val)}</span>
								))}
							</div>
						);
					}
					return typeof cellValue === "object" ? JSON.stringify(cellValue) : cellValue;
			}
		},
		[navigate, setDeleteLookbookId, confirmationModal]
	);

	// handling the lookbook export in csv format
	const handleLookbookCSVExport = () => {
		const headers = [
			{ label: "ID", key: "_id" },
			{ label: "Name", key: "name" },
			{ label: "Description", key: "description" },
			{ label: "Status", key: "status" },
			{ label: "Total Price", key: "totalPrice" },
			{ label: "Products", key: "productCount" },
			{ label: "Sections", key: "sectionCount" },
		];

		const finalData = (data?.getLookBooks?.lookBooks || [])
			.filter(
				(item: LookBookType) => selectedLookbookKeys !== "all" && selectedLookbookKeys.has(item._id)
			)
			.map((lookbook: LookBookType) => ({
				_id: lookbook._id,
				name: lookbook.name,
				description: lookbook.description,
				status: lookbook.status,
				totalPrice: lookbook.totalPrice,
				productCount: lookbook.productIds?.length || 0,
				sectionCount: lookbook.sections?.length || 0,
			}));

		return {
			data: finalData,
			headers,
		};
	};

	// handling of the export and import process
	const exportImportDropDownItems = [
		{
			key: "export",
			label: "Export",
			description: "Export your lookbooks to a CSV file",
			icon: <CiExport className="text-lg" />,
			onPress: handleLookbookCSVExport,
		},
		{
			key: "import",
			label: "Import",
			description: "Import Multiple Lookbooks in your store",
			icon: <CiImport className="text-lg" />,
			onPress: () => {},
		},
	];

	const showItems = {
		columnsToggle: true,
		exportImportButton: true,
		filters: false,
		searchBar: true,
	};

	// top header content for the table
	const topHeader = useMemo(() => {
		return (
			<TopHeader
				columns={columns}
				exportImportDropDownItems={exportImportDropDownItems}
				filterValue={lookbookFilterText}
				onOpen={mainModal.onOpen}
				onSearchChange={setLookbookFilterText}
				showItems={showItems}
				setVisibleColumns={(cols) => setLookbookVisibleColumns(cols)}
				visibleColumns={lookbookVisibleColumns}
			/>
		);
	}, [lookbookVisibleColumns, lookbookFilterText, mainModal.onOpen]);

	// top header when any of the row is selected
	const topSelectedContent = useMemo(() => {
		return (
			<div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 p-2 rounded-md">
				<p>
					{selectedLookbookKeys === "all"
						? data?.getLookBooks?.lookBooks.length
						: selectedLookbookKeys.size}{" "}
					of {data?.getLookBooks?.lookBooks.length} Selected
				</p>
				<RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
				<div className="flex flex-wrap md:flex-nowrap gap-x-3 gap-y-2">
					<Button
						radius="full"
						color="primary"
						size="sm"
						onPress={handleLookbookCSVExport}
						variant="ghost"
						className="border"
						startContent={<BiExport />}
					>
						<CSVLink
							asyncOnClick={true}
							filename="lookbooks-export.csv"
							headers={handleLookbookCSVExport().headers}
							data={handleLookbookCSVExport().data}
						>
							Export
						</CSVLink>
					</Button>
					<Dropdown>
						<DropdownTrigger className="flex !rounded-[40px]">
							<Button
								startContent={<IoEyeOutline className="text-small " />}
								variant="ghost"
								size="sm"
								color="primary"
								className="border"
								endContent={<IoChevronDown />}
							>
								More Actions
							</Button>
						</DropdownTrigger>
						<DropdownMenu
							disallowEmptySelection
							aria-label="Table Columns"
							closeOnSelect={true}
							selectionMode="multiple"
							classNames={{
								list: "w-full flex-none",
							}}
						>
							<DropdownItem
								key={"delete"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<IoTrash />}
								onPress={() => bulkDeleteModal.onOpen()}
							>
								Delete
							</DropdownItem>
						</DropdownMenu>
					</Dropdown>
				</div>
			</div>
		);
	}, [selectedLookbookKeys, data?.getLookBooks?.lookBooks]);

	const finalSelectedKeys = useMemo(() => {
		if (selectedLookbookKeys === "all") {
			return (data?.getLookBooks?.lookBooks || []).map((item: LookBookType) => item._id);
		}
		return Array.from(selectedLookbookKeys.values());
	}, [selectedLookbookKeys, data?.getLookBooks?.lookBooks]);

	// const handleAction = () => {
	// 	refetch({
	// 		filter: {
	// 			status: lookbookFilters.status,
	// 			search: lookbookFilterText,
	// 		},
	// 	}).then(() => mainModal.onClose());
	// };

	if (!loading && error) {
		addToast({
			title: "Failed to load lookbooks",
			color: "danger",
			shouldShowTimeoutProgress: true,
		});
	}

	return (
		<>
			<div>
				<Breadcrumbs>
					<BreadcrumbItem>Home</BreadcrumbItem>
					<BreadcrumbItem>Lookbooks</BreadcrumbItem>
				</Breadcrumbs>
			</div>

			<ModalComponent
				isOpen={confirmationModal.isOpen}
				onOpenChange={confirmationModal.onOpenChange}
				id="modal-2"
				onPress={() => deleteLookbook({ variables: { deleteLookBookId: deleteLookbookId } })}
				modalHeader={"Lookbook Delete Confirmation"}
				saveButtonText={"Delete"}
				className={{
					saveColor: "danger",
				}}
				isLoading={deleteLoading}
			>
				Are you sure you want to delete this lookbook?
			</ModalComponent>

			<ModalComponent
				isOpen={bulkDeleteModal.isOpen}
				onOpenChange={bulkDeleteModal.onOpenChange}
				id="modal-3"
				size="lg"
				isLoading={bulkDeleteLoading}
				onPress={() => {
					bulkDeleteLookbooks({
						variables: {
							lookBookIds: finalSelectedKeys,
						},
					}).then(() => {
						bulkDeleteModal.onClose();
					});
				}}
			>
				<div className="flex flex-col items-center gap-y-2">
					<FiAlertTriangle className="text-3xl text-red-500" />
					<h5>Are you sure you want to delete the selected lookbooks?</h5>
				</div>
			</ModalComponent>

			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<h1>Lookbooks {totalCount ? `(${totalCount})` : null}</h1>
				<Button
					size="sm"
					radius="full"
					color="primary"
					startContent={<BiPlus />}
					onPress={() => navigate("/admin/store/add-lookbook")}
				>
					Add New Lookbook
				</Button>
			</div>
			{/* <TopDrawer
				isOpen={mainModal.isOpen}
				onOpenChange={mainModal.onOpenChange}
				drawerHeader="Filters"
				isLoading={loading}
				handleAction={handleAction}
			></TopDrawer> */}
			<TableComponent<LookBookType>
				columns={columns}
				isLoading={isInitialLoading}
				hasMore={hasMore}
				list={{ items: data?.getLookBooks?.lookBooks || [] }}
				renderCell={renderCell}
				visibleColumns={lookbookVisibleColumns}
				topContent={topHeader}
				topSelectedContent={topSelectedContent}
				selectedKeys={selectedLookbookKeys}
				setSelectedKeys={(keys) => setSelectedLookbookKeys(keys)}
				handleLoadMore={() =>
					fetchMore({
						variables: {
							offset: data?.getLookBooks?.lookBooks?.length || 0,
						},
					})
				}
			/>
		</>
	);
};

export default LookBook;
