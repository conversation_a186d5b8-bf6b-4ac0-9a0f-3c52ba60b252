import { useMutation, useQuery } from "@apollo/client";
import { Button, useDisclosure, addToast } from "@heroui/react";
import { useState } from "react";
import {
	CREATE_COLLECTION,
	DELETE_COLLECTION,
	GET_COLLECTIONS,
	UPDATE_COLLECTION,
} from "../graphql/collections";
import CardSkeleton from "../components/collections/CardSkeleton";
import CollectionCard from "../components/collections/CollectionCard";
import { CollectionType } from "../types/colletctionType";
import ModalComponent from "../components/ModalComponent";
import EditCollectionForm from "../components/collections/EditCollectionForm";
import { FiAlertTriangle } from "react-icons/fi";
import { useMutationHandler } from "../hooks/useMutationStatusHandler";
import { BiPlus } from "react-icons/bi";
import { PUT_FILE } from "../graphql/files";

const Collections = () => {
	const [collectionData, setCollectionData] = useState({
		collectionName: "",
		collectionId: "",
		collectionImg: null as File | null | string,
		file: null as File | null, // Add file property to track the actual File object
	});

	const { data, loading, error } = useQuery(GET_COLLECTIONS);
	const [getSignedUrlToPutObject] = useMutation(PUT_FILE);

	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
	const {
		isOpen: isCollectionDeleteOpen,
		onOpen: onCollectionDeleteOpen,
		onOpenChange: onCollectionDeleteOpenChange,
		onClose: onCollectionDeleteClose,
	} = useDisclosure({ id: "modal-2" });

	const {
		isOpen: isAddCollectionOpen,
		onOpen: onAddCollectionOpen,
		onOpenChange: onAddCollectionOpenChange,
		onClose: onAddCollectionClose,
	} = useDisclosure({ id: "modal-3" });
	const [
		updateCategory,
		{ data: updateCollectionData, error: updateCollectionError, loading: updateCollectionLoading },
	] = useMutation(UPDATE_COLLECTION, {
		refetchQueries: [GET_COLLECTIONS, "GetCategories"],
		awaitRefetchQueries: true,
	});

	const [
		deleteCategory,
		{ loading: deleteCollectionLoading, error: deleteCollectionError, data: deleteCollectionData },
	] = useMutation(DELETE_COLLECTION, {
		refetchQueries: [GET_COLLECTIONS, "GetCategories"],
	});

	const [
		createCollection,
		{ loading: createCollectionLoading, error: createCollectionError, data: createCollectionData },
	] = useMutation(CREATE_COLLECTION, {
		refetchQueries: [GET_COLLECTIONS, "GetCategories"],
	});

	useMutationHandler({
		data: updateCollectionData,
		loading: updateCollectionLoading,
		error: updateCollectionError,
		successMessage: "Collection updated successfully",
		onClose,
	});

	useMutationHandler({
		data: deleteCollectionData,
		loading: deleteCollectionLoading,
		error: deleteCollectionError,
		successMessage: "Collection deleted successfully",
		onClose: onCollectionDeleteClose,
	});

	useMutationHandler({
		data: createCollectionData,
		loading: createCollectionLoading,
		error: createCollectionError,
		successMessage: "Collection created successfully",
		onClose: onAddCollectionClose,
	});

	const handleCollectionEdit = (collectionId: string, collectionName: string, imgsrc: string) => {
		onOpen();
		setCollectionData((prev) => ({
			...prev,
			collectionId,
			collectionName,
			collectionImg: imgsrc,
		}));
	};

	const handleCollectionDelete = (collectionId: string) => {
		onCollectionDeleteOpen();
		setCollectionData((prev) => ({
			...prev,
			collectionId,
		}));
	};

	// Helper function to upload image to S3 and get CDN URL
	const uploadImageToS3 = async (file: File, collectionName: string): Promise<string> => {
		const filesData = [
			{
				fileName: file.name,
				extension: file.name.split(".").pop(),
				contentType: file.type,
				fileKey: `collections/${collectionName}-${Date.now()}/${file.name}`,
			},
		];

		const { data: signedUrlsResponse } = await getSignedUrlToPutObject({
			variables: { filesData },
		});

		if (!signedUrlsResponse || !signedUrlsResponse.getSignedUrlToPutObject) {
			throw new Error("Failed to get signed URLs");
		}

		const fileInfo = signedUrlsResponse.getSignedUrlToPutObject[0];
		const response = await fetch(fileInfo.url, {
			method: "PUT",
			body: file,
			headers: { "Content-Type": file.type },
		});

		if (!response.ok) {
			throw new Error(`Upload failed for ${file.name}`);
		}

		return fileInfo.cdnUrl;
	};

	const onCollectionEdit = async () => {
		try {
			console.log(collectionData.collectionId, " collection id");

			let imageUrl = "";

			// If there's a new file to upload, upload it to S3
			if (collectionData.file) {
				imageUrl = await uploadImageToS3(collectionData.file, collectionData.collectionName);
			} else if (typeof collectionData.collectionImg === "string") {
				// Use existing image URL if no new file
				imageUrl = collectionData.collectionImg;
			} else {
				// Fallback to default image if no image provided
				imageUrl =
					"https://fastly.picsum.photos/id/866/200/300.jpg?hmac=rcadCENKh4rD6MAp6V_ma-AyWv641M4iiOpe1RyFHeI";
			}

			await updateCategory({
				variables: {
					updateCategoryId: collectionData.collectionId,
					input: {
						imgsrc: imageUrl,
						name: collectionData.collectionName,
					},
				},
			});

			onClose();
			setCollectionData({
				collectionName: "",
				collectionId: "",
				collectionImg: null,
				file: null,
			});
		} catch (error) {
			console.error("Error updating collection:", error);
			addToast({
				title: "Error uploading image or updating collection",
				color: "danger",
			});
		}
	};

	const onCollectionAdd = async () => {
		try {
			let imageUrl = "";

			// If there's a file to upload, upload it to S3
			if (collectionData.file) {
				imageUrl = await uploadImageToS3(collectionData.file, collectionData.collectionName);
			} else {
				// Fallback to default image if no image provided
				imageUrl =
					"https://fastly.picsum.photos/id/866/200/300.jpg?hmac=rcadCENKh4rD6MAp6V_ma-AyWv641M4iiOpe1RyFHeI";
			}

			createCollection({
				variables: {
					input: {
						imgsrc: imageUrl,
						name: collectionData.collectionName,
					},
				},
			}).then(() => {
				onAddCollectionClose();
				setCollectionData({
					collectionName: "",
					collectionId: "",
					collectionImg: null,
					file: null,
				});
			});
		} catch (error) {
			console.error("Error creating collection:", error);
			addToast({
				title: "Error uploading image or creating collection",
				color: "danger",
			});
		}
	};

	const handleAddCollectionOpen = () => {
		onAddCollectionOpen();
		setCollectionData({
			collectionName: "",
			collectionId: "",
			collectionImg: null,
			file: null,
		});
	};

	const onCollectionDelete = () => {
		deleteCategory({
			variables: {
				deleteCategoryId: collectionData.collectionId,
			},
		}).then(() => {
			onCollectionDeleteClose();
			setCollectionData({
				collectionName: "",
				collectionId: "",
				collectionImg: null,
				file: null,
			});
		});
	};

	return (
		<>
			<ModalComponent
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				onPress={onCollectionEdit}
				modalHeader={"Edit Collection"}
				isLoading={updateCollectionLoading}
			>
				<EditCollectionForm collectionData={collectionData} setCollectionData={setCollectionData} />
			</ModalComponent>
			<ModalComponent
				isOpen={isAddCollectionOpen}
				onOpenChange={onAddCollectionOpenChange}
				onPress={onCollectionAdd}
				modalHeader={"Add Collection"}
				isLoading={updateCollectionLoading}
				id="modal-3"
			>
				<EditCollectionForm collectionData={collectionData} setCollectionData={setCollectionData} />
			</ModalComponent>
			<ModalComponent
				modalHeader={"Delete Collection"}
				isOpen={isCollectionDeleteOpen}
				onOpenChange={onCollectionDeleteOpenChange}
				id="modal-2"
				onPress={onCollectionDelete}
				saveButtonText={"Yes Delete"}
				isLoading={deleteCollectionLoading}
			>
				<div className="flex flex-col items-center gap-y-2">
					<FiAlertTriangle className="text-3xl text-red-500" />
					<h5>Are you sure you want to delete this collection?</h5>
				</div>
			</ModalComponent>
			<div>
				<div className="flex w-full justify-between">
					<h1 className="mb-10">Collections ({data?.getCategories?.totalCount})</h1>
					<Button
						size="sm"
						radius="full"
						startContent={<BiPlus />}
						color="primary"
						onPress={handleAddCollectionOpen}
					>
						Add Collection
					</Button>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5">
					{data?.getCategories?.categories.map((collection: CollectionType) => (
						<CollectionCard
							collectionId={collection._id}
							imgsrc={
								collection.imgsrc
									? collection.imgsrc
									: "https://fastly.picsum.photos/id/866/200/300.jpg?hmac=rcadCENKh4rD6MAp6V_ma-AyWv641M4iiOpe1RyFHeI"
							}
							name={collection.name}
							key={collection._id}
							handleCollectionEdit={handleCollectionEdit}
							handleCollectionDelete={handleCollectionDelete}
						/>
					))}
				</div>
			</div>
		</>
	);
};

export default Collections;
