import React from "react";
import { FiSettings, <PERSON><PERSON>lock, FiArrowLeft } from "react-icons/fi";
import { Button } from "@heroui/react";

const UnderConstructionPage: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-md w-full px-6 py-6">
        <div className="flex justify-center mb-6">
          <div className="h-24 w-24 bg-lightPrimary rounded-full flex items-center justify-center">
            <FiSettings className="h-12 w-12 text-primary animate-spin-slow" />
          </div>
        </div>

        <h2 className="text-center text-3xl font-extrabold text-gray-900 mb-4">
          Under Construction
        </h2>

        <p className="text-center text-md text-gray-600 mb-8">
          We're working hard to bring you something amazing. This page will be
          available soon.
        </p>

        <div className="flex items-center justify-center mb-6">
          <div className="flex items-center text-sm text-gray-500">
            <FiClock className="mr-2" />
            <span>Estimated completion: 20 August 2025</span>
          </div>
        </div>

        {/* <div className="flex justify-center">
          <Button
            as="a"
            href="/admin/products"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiArrowLeft className="mr-2" />
            Back to Homepage
          </Button>
        </div> */}
      </div>
    </div>
  );
};

export default UnderConstructionPage;
