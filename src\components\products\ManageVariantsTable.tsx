import { useMemo } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@heroui/react";
import { useBoundStore } from "../../store/store";
import { ProductVariantsType } from "../../types/productType";
import { IoEyeOffOutline, IoEyeOutline } from "react-icons/io5";

const columns = [
  { key: "selectedOptions", label: "Variant" },
  { key: "priceDifference", label: "Price Difference(+/-)" },
  { key: "variantPrice", label: "Variant Price" },
  { key: "stockStatus", label: "Status" },
  { key: "stockQuantity", label: "Inventory" },
  { key: "visibility", label: "Visibility" },
];

export default function ManageVariantsTable() {
  const { variants, trackInventory } = useBoundStore();

  const renderCell = (item: ProductVariantsType, columnKey: string) => {
    switch (columnKey) {
      case "selectedOptions":
        return (
          <div className="whitespace-nowrap text-nowrap">
            {item.selectedOptions?.join(" | ") || "-"}
          </div>
        );

      case "priceDifference":
        return <div>₹{item.priceDifference?.toFixed(2) ?? "0.00"}</div>;

      case "variantPrice":
        return <div>₹{item.variantPrice?.toFixed(2) ?? "0.00"}</div>;

      case "stockStatus":
        return <div>{item.stockStatus ?? "-"}</div>;

      case "stockQuantity":
        return <div>{item.stockQuantity ?? 0}</div>;

      case "visibility":
        return item.visibility ? (
          <IoEyeOutline title="Visible" className="text-textStandard text-xl" />
        ) : (
          <IoEyeOffOutline
            title="Hidden"
            className="text-textStandard text-xl"
          />
        );

      default:
        const value = item[columnKey as keyof ProductVariantsType];
        if (Array.isArray(value)) {
          return (
            <div>
              {value.map((val, idx) => (
                <span key={idx}>{JSON.stringify(val)}</span>
              ))}
            </div>
          );
        }
        return typeof value === "object" ? JSON.stringify(value) : value ?? "-";
    }
  };

  const calculatedColumns = useMemo(() => {
    return trackInventory
      ? columns.filter((col) => col.key !== "stockStatus")
      : columns.filter((col) => col.key !== "stockQuantity");
  }, [trackInventory]);

  return (
    <div className="flex flex-col gap-3">
      <Table
        aria-label="Manage Product Variants"
        className="productOptionsTable gap-0"
        classNames={{
          base: "max-h-[600px] overflow-auto",
          table: "min-h-[100px]",
          wrapper: "scrollbar p-0 shadow-none rounded-none mt-0",
          thead: "!rounded-none",
          th: "bg-[#c3e5ff] dark:bg-slate-900 py-4 !rounded-none font-semibold text-primaryBlack text-sm",
          tr: "hover:bg-lightPrimary/20 dark:hover:bg-slate-600 h-14 border-b",
        }}
      >
        <TableHeader columns={calculatedColumns}>
          {(column) => (
            <TableColumn key={column.key}>{column.label}</TableColumn>
          )}
        </TableHeader>
        <TableBody items={variants}>
          {(item) => (
            <TableRow key={item._id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey as string)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
