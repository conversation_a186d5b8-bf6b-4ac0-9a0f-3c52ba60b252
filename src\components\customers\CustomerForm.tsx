import {
	Button,
	Input,
	Select,
	SelectItem,
	// Dropdown,
	// DropdownTrigger,
	// DropdownMenu,
	// DropdownItem,
	Checkbox,
	// DatePicker,
	Divider,
	DateInput,
	NumberInput,
} from "@heroui/react";
import {
	Controller,
	useForm,
	useFieldArray,
	// UseFormSetValue,
	// UseFormWatch,
	// Control,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, RefObject, useMemo, useCallback, memo } from "react";
import { BiPlus, BiTrash } from "react-icons/bi";
import {
	CustomerFormData,
	customerValidationSchema,
} from "../../validation/customerValidationSchema";
import { CalendarDate, parseDate } from "@internationalized/date";
import { AddressType, CustomerMemberStatus, CustomerType } from "../../types/customerType";
import { CustomerUserGender } from "../../types/customerType";
import { useQuery } from "@apollo/client";
import { GET_LABELS } from "../../graphql/customers";

// Define field types for dynamic field addition
type FieldType =
	| "email"
	| "phone"
	| "addresses"
	| "company"
	| "position"
	| "language"
	| "vatId"
	| "birthdate"
	| "customField";

interface CustomerFormProps {
	initialData?: CustomerType | null;
	onSubmit: (data: CustomerFormData) => void;
	isLoading?: boolean;
	formRef?: RefObject<HTMLFormElement>;
}

// Memoized Address Type Selector Component
const AddressTypeSelector = memo(
	({
		field,
		availableTypes,
		errors,
	}: {
		field: any;
		availableTypes: AddressType[];
		errors: any;
		index: number;
	}) => (
		<Select
			label="Type"
			selectedKeys={[field.value || AddressType.WORK]}
			onSelectionChange={(keys) => {
				const key = Array.from(keys)[0] as string;
				field.onChange(key);
			}}
			placeholder="Select address type"
			size="sm"
			labelPlacement="outside"
			classNames={{
				label: "text-sm",
			}}
			errorMessage={errors?.addressType?.message}
			isInvalid={!!errors?.addressType}
		>
			{availableTypes.map((type) => (
				<SelectItem key={type}>{type}</SelectItem>
			))}
		</Select>
	)
);

AddressTypeSelector.displayName = "AddressTypeSelector";

const CustomerForm = ({ initialData, onSubmit, formRef }: CustomerFormProps) => {
	// State for managing which fields are visible
	const [visibleFields] = useState<Set<FieldType>>(new Set(["email", "phone", "addresses"]));
	// const [customFieldName, setCustomFieldName] = useState("");
	// const [showAddFieldDropdown, setShowAddFieldDropdown] = useState(false);

	//get the labels data
	// need to add the pagination correctly here later on
	const {
		data: labelsData,
		loading: labelsLoading,
		//error: labelsError,
	} = useQuery(GET_LABELS, {
		variables: { limit: 100, offset: 0, filters: {} },
		notifyOnNetworkStatusChange: true,
	});

	console.log(initialData, " initial data");

	// Initialize form with react-hook-form
	const {
		control,
		handleSubmit,
		formState: { errors },
		watch,
		// setValue,
	} = useForm<CustomerFormData>({
		resolver: zodResolver(customerValidationSchema),
		defaultValues: initialData
			? {
					firstName: initialData.firstName || "",
					lastName: initialData.lastName || "",
					email: initialData.email || "",
					phoneNumber: initialData.phoneNumber || "",
					emailSubscribedStatus:
						initialData.emailSubscribedStatus || CustomerMemberStatus.NEVER_SUBSCRIBED,
					gender: initialData.gender || CustomerUserGender.MALE,
					dateOfBirth: initialData.dateOfBirth || "",
					countryCode: initialData.countryCode || "+91",
					labelIds: initialData.labelIds || [],
					addresses: initialData.addresses || [],
					// Other fields would be populated here if they existed in the initialData
			  }
			: {
					firstName: "",
					lastName: "",
					email: "",
					phoneNumber: "",
					gender: CustomerUserGender.MALE,
					dateOfBirth: "",
					countryCode: "+91",
					labelIds: [],
					// additionalEmails: [],
					// additionalPhones: [],
					// customFields: {},
					emailSubscribedStatus: CustomerMemberStatus.NEVER_SUBSCRIBED,
					addresses: [
						{
							_id: "",
							userId: "",
							addressType: AddressType.WORK,
							flat: 0,
							addressline1: "",
							addressline2: "",
							landmark: "",
							countryCode: "+91",
							phone: "",
							city: "",
							country: "India",
							states: "",
							pincode: "",
							primary: true,
							// createdAt: "",
							// updatedAt: "",
						},
					],
			  },
	});
	console.log(errors, " errors");

	// Field arrays for multiple emails, phones, and addresses
	const {
		fields: emailFields,
		// append: appendEmail,
		remove: removeEmail,
	} = useFieldArray({
		control,
		name: "additionalEmails",
	});

	const {
		fields: phoneFields,
		// append: appendPhone,
		remove: removePhone,
	} = useFieldArray({
		control,
		name: "additionalPhones",
	});

	const {
		fields: addressFields,
		append: appendAddress,
		remove: removeAddress,
	} = useFieldArray({
		control,
		name: "addresses",
	});

	// Memoized function to get available address types for a specific address index
	const getAvailableAddressTypes = useCallback(
		(currentIndex: number, includeCurrentValue = false) => {
			// Only watch the specific address types we need, not the entire addresses array
			const usedTypes: AddressType[] = [];

			for (let i = 0; i < addressFields.length; i++) {
				if (i !== currentIndex) {
					const addressType = watch(`addresses.${i}.addressType`);
					if (addressType) {
						usedTypes.push(addressType as AddressType);
					}
				}
			}

			const availableTypes = Object.values(AddressType).filter((type) => !usedTypes.includes(type));

			// If includeCurrentValue is true, always include the current value even if it would normally be filtered out
			if (includeCurrentValue) {
				const currentValue = watch(`addresses.${currentIndex}.addressType`) as AddressType;
				if (currentValue && !availableTypes.includes(currentValue)) {
					availableTypes.push(currentValue);
				}
			}

			return availableTypes;
		},
		[addressFields.length, watch]
	);

	// Handle adding a new field type
	// const handleAddField = (fieldType: FieldType) => {
	// 	const newVisibleFields = new Set(visibleFields);
	// 	newVisibleFields.add(fieldType);
	// 	setVisibleFields(newVisibleFields);
	// 	setShowAddFieldDropdown(false);
	// };

	// Handle adding a new custom field
	// const handleAddCustomField = () => {
	// 	if (customFieldName.trim()) {
	// 		setValue(`customFields.${customFieldName.trim()}`, "");
	// 		setCustomFieldName("");
	// 	}
	// };

	// Handle adding additional email, phone, or address
	// const handleAddEmail = () => {
	// 	appendEmail({ email: "", type: "Work", isSubscribed: false });
	// };

	// const handleAddPhone = () => {
	// 	appendPhone({ phone: "", type: "Mobile", isSubscribed: false });
	// };

	const handleAddAddress = useCallback(() => {
		// Get the next available address type
		const nextIndex = addressFields.length;
		const availableTypes = getAvailableAddressTypes(nextIndex);
		const defaultType = availableTypes.length > 0 ? availableTypes[0] : AddressType.OTHER;

		appendAddress({
			_id: "",
			userId: "",
			addressType: defaultType,
			flat: 0,
			addressline1: "",
			addressline2: "",
			landmark: "",
			countryCode: "+91",
			phone: "",
			city: "",
			country: "India",
			states: "",
			pincode: "",
			primary: false,
			// createdAt: "",
			// updatedAt: "",
		});
	}, [addressFields.length, getAvailableAddressTypes, appendAddress]);

	// Memoized button state for Add Address
	const addAddressButtonState = useMemo(() => {
		const availableTypes = getAvailableAddressTypes(addressFields.length);
		const isDisabled = availableTypes.length === 0;
		return {
			isDisabled,
			title: isDisabled ? "All address types are already in use" : "Add a new address",
			text: isDisabled ? "All Address Types Used" : "Add Address",
		};
	}, [addressFields.length, getAvailableAddressTypes]);

	// Submit handler
	const onFormSubmit = useCallback(
		(data: CustomerFormData) => {
			onSubmit(data);
		},
		[onSubmit]
	);

	return (
		<form ref={formRef} onSubmit={handleSubmit(onFormSubmit)} className="space-y-3 scrollbar">
			{/* Name Section */}
			<div className="space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Controller
						control={control}
						name="firstName"
						render={({ field }) => (
							<Input
								label="First name"
								{...field}
								isRequired
								errorMessage={errors.firstName?.message}
								isInvalid={!!errors.firstName}
								labelPlacement="outside"
								placeholder="First name"
								size="sm"
								classNames={{
									inputWrapper: "rounded-md",
									label: "text-sm",
								}}
							/>
						)}
					/>
					<Controller
						control={control}
						name="lastName"
						render={({ field }) => (
							<Input
								label="Last name"
								{...field}
								placeholder="Last name"
								isRequired
								errorMessage={errors.lastName?.message}
								isInvalid={!!errors.lastName}
								labelPlacement="outside"
								size="sm"
								classNames={{
									inputWrapper: "rounded-md",
									label: "text-sm",
								}}
							/>
						)}
					/>
				</div>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5">
				<Controller
					control={control}
					name="gender"
					render={({ field }) => (
						<Select
							label="Gender"
							selectedKeys={[field.value || CustomerUserGender.MALE]}
							onSelectionChange={(keys) => {
								const key = Array.from(keys)[0] as CustomerUserGender;
								field.onChange(key);
							}}
							size="sm"
							labelPlacement="outside"
							className="max-w-full w-full"
							classNames={{
								label: "text-sm",
							}}
						>
							<SelectItem key={CustomerUserGender.MALE}>{CustomerUserGender.MALE}</SelectItem>
							<SelectItem
								key={CustomerUserGender.FEMALE}
								//svalue={CustomerMemberStatus.NEVER_SUBSCRIBED}
							>
								{CustomerUserGender.FEMALE}
							</SelectItem>
							<SelectItem key={CustomerUserGender.OTHER}>{CustomerUserGender.OTHER}</SelectItem>
						</Select>
					)}
				/>
				<Controller
					name="dateOfBirth"
					control={control}
					render={({ field }) => (
						<DateInput
							size="sm"
							labelPlacement="outside"
							className="max-w-full w-full"
							{...field}
							onChange={(value) => {
								if (value) {
									const date = new CalendarDate(value.year, value.month, value.day);
									field.onChange(date.toString());
								}
							}}
							value={field.value ? parseDate(field.value) : null}
							label={"Birth date"}
							placeholderValue={new CalendarDate(1995, 11, 6)}
						/>
					)}
				/>
			</div>
			{/* Primary Email Section */}
			<div className="mt-5">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Controller
						control={control}
						name="email"
						render={({ field }) => (
							<Input
								{...field}
								placeholder="Email"
								errorMessage={errors.email?.message}
								isInvalid={!!errors.email}
								label="Primary email"
								labelPlacement="outside"
								size="sm"
								classNames={{
									inputWrapper: "rounded-md",
									label: "text-sm",
								}}
							/>
						)}
					/>
					<div className="grid grid-cols-12 gap-2 w-full">
						<Controller
							control={control}
							name="countryCode"
							render={({ field }) => (
								<Select
									label="Code"
									labelPlacement="outside"
									defaultSelectedKeys={["+91"]}
									className="col-span-5 mt-0"
									size="sm"
									classNames={{
										label: "text-sm",
										innerWrapper: "min-w-32",
									}}
									onSelectionChange={(keys) => {
										const key = Array.from(keys)[0] as string;
										field.onChange(key);
									}}
									{...field}
								>
									<SelectItem key="+91">+91 (India)</SelectItem>
									<SelectItem key="+1">+1 (US)</SelectItem>
									<SelectItem key="+44">+44 (UK)</SelectItem>
									<SelectItem key="+61">+61 (Australia)</SelectItem>
								</Select>
							)}
						/>

						<Controller
							control={control}
							name="phoneNumber"
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label="Phone"
									placeholder="Phone number"
									errorMessage={errors.phoneNumber?.message}
									isInvalid={!!errors.phoneNumber}
									className="col-span-7"
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>

					{/* <label className="text-sm font-medium">Primary email</label> */}
				</div>
			</div>
			<div className="mt-5 grid grid-cols-1 md:grid-cols-2 gap-4">
				<Controller
					control={control}
					name="emailSubscribedStatus"
					render={({ field }) => (
						<Select
							label="Subscription status"
							selectedKeys={[field.value || CustomerMemberStatus.NEVER_SUBSCRIBED]}
							onSelectionChange={(keys) => {
								const key = Array.from(keys)[0] as CustomerMemberStatus;
								field.onChange(key);
							}}
							size="sm"
							labelPlacement="outside"
							className="max-w-full w-full"
							classNames={{
								label: "text-sm",
							}}
						>
							<SelectItem key={CustomerMemberStatus.NEVER_SUBSCRIBED}>Never subscribed</SelectItem>
							<SelectItem
								key={CustomerMemberStatus.SUBSCRIBED}
								//value={CustomerMemberStatus.SUBSCRIBED}
							>
								Subscribed
							</SelectItem>
							<SelectItem
								key={CustomerMemberStatus.SITE_MEMBER}
								// value={CustomerMemberStatus.SITE_MEMBER}
							>
								Site member
							</SelectItem>
						</Select>
					)}
				/>
				<Controller
					name="labelIds"
					control={control}
					render={({ field }) => (
						<Select
							label="Labels"
							size="sm"
							labelPlacement="outside"
							className="max-w-full w-full"
							classNames={{
								label: "text-sm",
							}}
							isLoading={labelsLoading}
							selectionMode="multiple"
							// Map array of IDs to Set for selectedKeys
							selectedKeys={field.value ? new Set(field.value) : new Set()}
							// Transform selected keys to array of IDs
							onSelectionChange={(keys) => {
								const selectedIds = Array.from(keys) as string[];

								field.onChange(selectedIds);
							}}
							placeholder="Select labels"
						>
							{labelsData?.getLabelsByFilter?.data?.map((label: { _id: string; name: string }) => (
								<SelectItem key={label._id}>{label.name}</SelectItem>
							))}
						</Select>
					)}
				/>
			</div>

			{/* Additional Emails */}
			{emailFields.map((field, index) => (
				<div key={field.id} className="space-y-2">
					<div className="flex items-center justify-between">
						<label className="text-sm font-medium">Additional email</label>
						<Button
							isIconOnly
							size="sm"
							variant="flat"
							color="danger"
							onPress={() => removeEmail(index)}
						>
							<BiTrash />
						</Button>
					</div>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="md:col-span-2">
							<Controller
								control={control}
								name={`additionalEmails.${index}.email`}
								render={({ field }) => (
									<Input
										{...field}
										placeholder="Email"
										label="Email"
										size="sm"
										errorMessage={errors.additionalEmails?.[index]?.email?.message}
										isInvalid={!!errors.additionalEmails?.[index]?.email}
										classNames={{
											inputWrapper: "rounded-md",
										}}
										labelPlacement="outside"
									/>
								)}
							/>
						</div>
						<Controller
							control={control}
							name={`additionalEmails.${index}.type`}
							render={({ field }) => (
								<Select
									selectedKeys={[field.value]}
									onSelectionChange={(keys) => {
										const key = Array.from(keys)[0] as string;
										field.onChange(key);
									}}
									labelPlacement="outside"
									label="Type"
									size="sm"
								>
									<SelectItem key="Work">Work</SelectItem>
									<SelectItem key="Personal">Personal</SelectItem>
									<SelectItem key="Other">Other</SelectItem>
								</Select>
							)}
						/>
					</div>
					<Controller
						control={control}
						name={`additionalEmails.${index}.isSubscribed`}
						render={({ field }) => (
							<Checkbox isSelected={field.value} onValueChange={field.onChange}>
								Subscribed to emails
							</Checkbox>
						)}
					/>
				</div>
			))}

			{/* {visibleFields.has("email") && (
				<Button
					size="sm"
					variant="light"
					color="primary"
					radius="full"
					startContent={<BiPlus />}
					onPress={handleAddEmail}
				>
					Add Email
				</Button>
			)} */}
			<Divider />

			{/* Primary Phone Section */}
			{visibleFields.has("phone") && (
				<div className="space-y-2 pt-4">
					<div className="grid grid-cols-2 gap-4">
						{/* <Select
							label="Type"
							size="sm"
							defaultSelectedKeys={["Mobile"]}
							labelPlacement="outside"
							classNames={{
								label: "text-sm",
							}}
						>
							<SelectItem key="Mobile">Mobile</SelectItem>
							<SelectItem key="Home">Home</SelectItem>
							<SelectItem key="Work">Work</SelectItem>
							<SelectItem key="Other">Other</SelectItem>
						</Select> */}
						{/* <Select
							label="Code"
							labelPlacement="outside"
							defaultSelectedKeys={["+91"]}
							className="md:col-span-1"
							size="sm"
							classNames={{
								label: "text-sm",
							}}
						>
							<SelectItem key="+91">+91 (India)</SelectItem>
							<SelectItem key="+1">+1 (US)</SelectItem>
							<SelectItem key="+44">+44 (UK)</SelectItem>
							<SelectItem key="+61">+61 (Australia)</SelectItem>
						</Select>
						<Controller
							control={control}
							name="phone"
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label="Phone"
									placeholder="Phone number"
									errorMessage={errors.phone?.message}
									isInvalid={!!errors.phone}
									className="md:col-span-1"
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/> */}
					</div>
				</div>
			)}

			{/* Additional Phones */}
			{phoneFields.map((field, index) => (
				<div key={field.id} className="space-y-2">
					<div className="flex items-center justify-between">
						<label className="text-sm font-medium">Additional phone</label>
						<Button
							isIconOnly
							size="sm"
							variant="light"
							color="danger"
							onPress={() => removePhone(index)}
						>
							<BiTrash className="text-xl" />
						</Button>
					</div>
					<div className="grid grid-cols-3 gap-4">
						<Controller
							control={control}
							name={`additionalPhones.${index}.type`}
							render={({ field }) => (
								<Select
									selectedKeys={[field.value]}
									onSelectionChange={(keys) => {
										const key = Array.from(keys)[0] as string;
										field.onChange(key);
									}}
									label="Type"
									labelPlacement="outside"
									size="sm"
									classNames={{
										label: "text-sm",
									}}
								>
									<SelectItem key="Mobile">Mobile</SelectItem>
									<SelectItem key="Home">Home</SelectItem>
									<SelectItem key="Work">Work</SelectItem>
									<SelectItem key="Other">Other</SelectItem>
								</Select>
							)}
						/>
						<Select
							label="Code"
							size="sm"
							labelPlacement="outside"
							classNames={{
								label: "text-sm",
							}}
							defaultSelectedKeys={["+91"]}
						>
							<SelectItem key="+91">+91 (India)</SelectItem>
							<SelectItem key="+1">+1 (US)</SelectItem>
							<SelectItem key="+44">+44 (UK)</SelectItem>
							<SelectItem key="+61">+61 (Australia)</SelectItem>
						</Select>
						<Controller
							control={control}
							name={`additionalPhones.${index}.phone`}
							render={({ field }) => (
								<Input
									{...field}
									label="Phone"
									labelPlacement="outside"
									size="sm"
									placeholder="Phone number"
									errorMessage={errors.additionalPhones?.[index]?.phone?.message}
									isInvalid={!!errors.additionalPhones?.[index]?.phone}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
					<Controller
						control={control}
						name={`additionalPhones.${index}.isSubscribed`}
						render={({ field }) => (
							<Checkbox
								classNames={{
									wrapper: "!rounded",
									base: "!rounded",
									label: "rounded-md",
								}}
								radius="sm"
								isSelected={field.value}
								onValueChange={field.onChange}
							>
								Subscribed to SMS
							</Checkbox>
						)}
					/>
				</div>
			))}

			{/* {visibleFields.has("phone") && (
				<Button
					size="sm"
					variant="light"
					color="primary"
					radius="full"
					startContent={<BiPlus />}
					onPress={handleAddPhone}
				>
					Add Phone
				</Button>
			)} */}
			{/* <Divider /> */}

			{/* Address Section */}
			{visibleFields.has("addresses") && (
				<>
					<div className="grid grid-cols-3 gap-4 pt-4">
						<Controller
							control={control}
							name="addresses.0.addressType"
							render={({ field }) => {
								const availableTypes = getAvailableAddressTypes(0, true);
								return (
									<AddressTypeSelector
										field={field}
										availableTypes={availableTypes}
										errors={errors.addresses?.[0]}
										index={0}
									/>
								);
							}}
						/>
						<Controller
							control={control}
							name="addresses.0.flat"
							render={({ field: { onChange, onBlur, value, ref } }) => (
								<NumberInput
									ref={ref}
									onValueChange={onChange}
									onBlur={onBlur}
									value={value}
									size="sm"
									labelPlacement="outside"
									placeholder="Flat"
									label="Flat"
									errorMessage={errors.addresses?.[0]?.flat?.message}
									isInvalid={!!errors.addresses?.[0]?.flat}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
						<Controller
							control={control}
							name="addresses.0.primary"
							render={({ field }) => (
								<Select
									label="Primary Address"
									selectedKeys={[field.value === true ? "Yes" : "No"]}
									onSelectionChange={(keys) => {
										const key = Array.from(keys)[0] as string;
										field.onChange(key === "Yes");
									}}
									size="sm"
									labelPlacement="outside"
									classNames={{
										label: "text-sm",
									}}
								>
									<SelectItem key="Yes">Yes</SelectItem>
									<SelectItem key="No">No</SelectItem>
								</Select>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 gap-4">
						<Controller
							control={control}
							name="addresses.0.addressline1"
							render={({ field }) => (
								<Input
									{...field}
									size="sm"
									labelPlacement="outside"
									placeholder="Address Line 1"
									label="Address Line 1"
									errorMessage={errors.addresses?.[0]?.addressline1?.message}
									isInvalid={!!errors.addresses?.[0]?.addressline1}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>

						<Controller
							control={control}
							name="addresses.0.addressline2"
							render={({ field }) => (
								<Input
									{...field}
									label="Address Line 2 (Optional)"
									size="sm"
									labelPlacement="outside"
									placeholder="Address Line 2 (Optional)"
									errorMessage={errors.addresses?.[0]?.addressline2?.message}
									isInvalid={!!errors.addresses?.[0]?.addressline2}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<Controller
							control={control}
							name="addresses.0.phone"
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label="Phone"
									placeholder="Phone number"
									errorMessage={errors.addresses?.[0]?.phone?.message}
									isInvalid={!!errors.addresses?.[0]?.phone}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
						<Controller
							control={control}
							name="addresses.0.landmark"
							render={({ field }) => (
								<Input
									{...field}
									size="sm"
									labelPlacement="outside"
									placeholder="Landmark"
									label="Landmark"
									errorMessage={errors.addresses?.[0]?.landmark?.message}
									isInvalid={!!errors.addresses?.[0]?.landmark}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>

						<Controller
							control={control}
							name="addresses.0.city"
							render={({ field }) => (
								<Input
									{...field}
									size="sm"
									labelPlacement="outside"
									placeholder="City"
									label="City"
									errorMessage={errors.addresses?.[0]?.city?.message}
									isInvalid={!!errors.addresses?.[0]?.city}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<Controller
							control={control}
							name="addresses.0.states"
							render={({ field }) => (
								<Input
									{...field}
									size="sm"
									labelPlacement="outside"
									placeholder="State"
									label="State"
									errorMessage={errors.addresses?.[0]?.states?.message}
									isInvalid={!!errors.addresses?.[0]?.states}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
						<Controller
							control={control}
							name="addresses.0.pincode"
							render={({ field }) => (
								<Input
									{...field}
									size="sm"
									labelPlacement="outside"
									placeholder="Pincode"
									label="Pin/Postal code"
									errorMessage={errors.addresses?.[0]?.pincode?.message}
									isInvalid={!!errors.addresses?.[0]?.pincode}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
						<Controller
							control={control}
							name="addresses.0.country"
							render={({ field }) => (
								<Select
									label="Country"
									selectedKeys={[field.value || "India"]}
									onSelectionChange={(keys) => {
										const key = Array.from(keys)[0] as string;
										field.onChange(key);
									}}
									size="sm"
									labelPlacement="outside"
								>
									<SelectItem key="India">India</SelectItem>
									<SelectItem key="United States">United States</SelectItem>
									<SelectItem key="Canada">Canada</SelectItem>
									<SelectItem key="United Kingdom">United Kingdom</SelectItem>
									<SelectItem key="Australia">Australia</SelectItem>
									<SelectItem key="Germany">Germany</SelectItem>
									<SelectItem key="France">France</SelectItem>
									<SelectItem key="Japan">Japan</SelectItem>
									<SelectItem key="China">China</SelectItem>
								</Select>
							)}
						/>
					</div>
				</>
			)}

			{/* Additional Addresses */}
			{addressFields.slice(1).map((field, index) => {
				const actualIndex = index + 1; // Since we're starting from index 1
				return (
					<div key={field.id} className="space-y-4 border-t pt-4">
						<div className="flex items-center justify-between">
							<label className="text-sm font-medium">Additional address</label>
							<Button
								isIconOnly
								size="sm"
								variant="light"
								color="danger"
								onPress={() => removeAddress(actualIndex)}
							>
								<BiTrash className="text-xl" />
							</Button>
						</div>
						<div className="grid grid-cols-3 gap-4">
							<Controller
								control={control}
								name={`addresses.${actualIndex}.addressType`}
								render={({ field }) => {
									const availableTypes = getAvailableAddressTypes(actualIndex, true);
									return (
										<AddressTypeSelector
											field={field}
											availableTypes={availableTypes}
											errors={errors.addresses?.[actualIndex]}
											index={actualIndex}
										/>
									);
								}}
							/>
							<Controller
								control={control}
								name={`addresses.${actualIndex}.flat`}
								render={({ field: { onChange, onBlur, value, ref } }) => (
									<NumberInput
										ref={ref}
										onValueChange={onChange}
										onBlur={onBlur}
										value={value}
										size="sm"
										labelPlacement="outside"
										placeholder="Flat"
										label="Flat"
										errorMessage={errors.addresses?.[actualIndex]?.flat?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.flat}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>
							<Controller
								control={control}
								name={`addresses.${actualIndex}.primary`}
								render={({ field }) => (
									<Select
										label="Primary"
										selectedKeys={[field.value === true ? "Yes" : "No"]}
										onSelectionChange={(keys) => {
											const key = Array.from(keys)[0] as string;
											field.onChange(key === "Yes");
										}}
										size="sm"
										labelPlacement="outside"
										classNames={{
											label: "text-sm",
										}}
									>
										<SelectItem key="Yes">Yes</SelectItem>
										<SelectItem key="No">No</SelectItem>
									</Select>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 gap-4">
							<Controller
								control={control}
								name={`addresses.${actualIndex}.addressline1`}
								render={({ field }) => (
									<Input
										{...field}
										size="sm"
										labelPlacement="outside"
										placeholder="Address Line 1"
										label="Address Line 1"
										errorMessage={errors.addresses?.[actualIndex]?.addressline1?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.addressline1}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>

							<Controller
								control={control}
								name={`addresses.${actualIndex}.addressline2`}
								render={({ field }) => (
									<Input
										{...field}
										label="Address Line 2 (Optional)"
										size="sm"
										labelPlacement="outside"
										placeholder="Address Line 2 (Optional)"
										errorMessage={errors.addresses?.[actualIndex]?.addressline2?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.addressline2}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<Controller
								control={control}
								name={`addresses.${actualIndex}.phone`}
								render={({ field }) => (
									<Input
										{...field}
										labelPlacement="outside"
										size="sm"
										label="Phone"
										placeholder="Phone number"
										errorMessage={errors.addresses?.[actualIndex]?.phone?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.phone}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>
							<Controller
								control={control}
								name={`addresses.${actualIndex}.landmark`}
								render={({ field }) => (
									<Input
										{...field}
										size="sm"
										labelPlacement="outside"
										placeholder="Landmark"
										label="Landmark"
										errorMessage={errors.addresses?.[actualIndex]?.landmark?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.landmark}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>

							<Controller
								control={control}
								name={`addresses.${actualIndex}.city`}
								render={({ field }) => (
									<Input
										{...field}
										size="sm"
										labelPlacement="outside"
										placeholder="City"
										label="City"
										errorMessage={errors.addresses?.[actualIndex]?.city?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.city}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<Controller
								control={control}
								name={`addresses.${actualIndex}.states`}
								render={({ field }) => (
									<Input
										{...field}
										size="sm"
										labelPlacement="outside"
										placeholder="State"
										label="State"
										errorMessage={errors.addresses?.[actualIndex]?.states?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.states}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>
							<Controller
								control={control}
								name={`addresses.${actualIndex}.pincode`}
								render={({ field }) => (
									<Input
										{...field}
										size="sm"
										labelPlacement="outside"
										placeholder="Pincode"
										label="Pin/Postal code"
										errorMessage={errors.addresses?.[actualIndex]?.pincode?.message}
										isInvalid={!!errors.addresses?.[actualIndex]?.pincode}
										classNames={{
											inputWrapper: "rounded-md",
											label: "text-sm",
										}}
									/>
								)}
							/>
							<Controller
								control={control}
								name={`addresses.${actualIndex}.country`}
								render={({ field }) => (
									<Select
										label="Country"
										selectedKeys={[field.value || "India"]}
										onSelectionChange={(keys) => {
											const key = Array.from(keys)[0] as string;
											field.onChange(key);
										}}
										size="sm"
										labelPlacement="outside"
									>
										<SelectItem key="India">India</SelectItem>
										<SelectItem key="United States">United States</SelectItem>
										<SelectItem key="Canada">Canada</SelectItem>
										<SelectItem key="United Kingdom">United Kingdom</SelectItem>
										<SelectItem key="Australia">Australia</SelectItem>
										<SelectItem key="Germany">Germany</SelectItem>
										<SelectItem key="France">France</SelectItem>
										<SelectItem key="Japan">Japan</SelectItem>
										<SelectItem key="China">China</SelectItem>
									</Select>
								)}
							/>
						</div>
					</div>
				);
			})}

			{visibleFields.has("addresses") && (
				<Button
					size="sm"
					variant="light"
					radius="full"
					color="primary"
					startContent={<BiPlus />}
					onPress={handleAddAddress}
					isDisabled={addAddressButtonState.isDisabled}
					title={addAddressButtonState.title}
				>
					{addAddressButtonState.text}
				</Button>
			)}

			{/* <Divider /> */}

			<div className="flex flex-col gap-4 pt-3 ">
				{/* Company Field */}
				{visibleFields.has("company") && (
					<div className="space-y-2">
						<Controller
							control={control}
							name="company"
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label="Company"
									placeholder="Company name"
									errorMessage={errors.company?.message}
									isInvalid={!!errors.company}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
				)}

				{/* Position Field */}
				{visibleFields.has("position") && (
					<div className="space-y-2">
						<Controller
							control={control}
							name="position"
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label="Position"
									placeholder="Job title or position"
									errorMessage={errors.position?.message}
									isInvalid={!!errors.position}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
				)}

				{/* Language Field */}
				{visibleFields.has("language") && (
					<div className="space-y-2">
						<Controller
							control={control}
							name="language"
							render={({ field }) => (
								<Select
									selectedKeys={field.value ? [field.value] : []}
									onSelectionChange={(keys) => {
										const key = Array.from(keys)[0] as string;
										field.onChange(key);
									}}
									classNames={{
										label: "text-sm",
									}}
									size="sm"
									placeholder="Select a language"
									label="Language"
									labelPlacement="outside"
								>
									<SelectItem key="English">English</SelectItem>
									<SelectItem key="Spanish">Spanish</SelectItem>
									<SelectItem key="French">French</SelectItem>
									<SelectItem key="German">German</SelectItem>
									<SelectItem key="Chinese">Chinese</SelectItem>
									<SelectItem key="Japanese">Japanese</SelectItem>
									<SelectItem key="Hindi">Hindi</SelectItem>
								</Select>
							)}
						/>
					</div>
				)}

				{/* VAT ID Field */}
				{visibleFields.has("vatId") && (
					<div>
						<Controller
							control={control}
							name="vatId"
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label="VAT ID"
									placeholder="VAT identification number"
									errorMessage={errors.vatId?.message}
									isInvalid={!!errors.vatId}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
				)}
				{/* Birthdate Field */}
				{visibleFields.has("birthdate") && (
					<div>
						<Controller
							control={control}
							name="birthdate"
							render={({ field }) => (
								<Input
									type="date"
									{...field}
									label="Birthdate"
									labelPlacement="outside"
									size="sm"
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
				)}
			</div>

			{/* Custom Fields */}
			{/* {watch("customFields") &&
				Object.keys(watch("customFields") || {}).map((fieldName) => (
					<div key={fieldName} className="space-y-2">
						<label className="text-sm font-medium">{fieldName}</label>
						<Controller
							control={control}
							name={`customFields.${fieldName}` as any}
							render={({ field }) => (
								<Input
									{...field}
									labelPlacement="outside"
									size="sm"
									label={fieldName}
									placeholder={`Enter ${fieldName}`}
									classNames={{
										inputWrapper: "rounded-md",
										label: "text-sm",
									}}
								/>
							)}
						/>
					</div>
				))} */}

			{/* Labels Section */}
			{/* <div className="space-y-2">
        <label className="text-sm font-medium">Labels</label>
        <div className="border rounded-md p-2 min-h-[40px]">
          <Button
            size="sm"
            variant="light"
            color="primary"
            startContent={<BiPlus />}
          >
            Add Label
          </Button>
        </div>
      </div> */}

			{/* Assignee Section */}
			{/* <div className="py-1">
				<Controller
					control={control}
					name="assignee"
					render={({ field }) => (
						<Select
							selectedKeys={field.value ? [field.value] : ["unassigned"]}
							onSelectionChange={(keys) => {
								const key = Array.from(keys)[0] as string;
								field.onChange(key);
							}}
							label="Assignee"
							size="sm"
							labelPlacement="outside"
							classNames={{
								label: "text-sm",
							}}
						>
							<SelectItem key="unassigned">Unassigned</SelectItem>
							<SelectItem key="user1">John Doe</SelectItem>
							<SelectItem key="user2">Jane Smith</SelectItem>
						</Select>
					)}
				/>
			</div> */}

			{/* Add New Field Button */}
			{/* <div className="absolute bottom-0 left-0 p-4">
				<Dropdown isOpen={showAddFieldDropdown} onOpenChange={setShowAddFieldDropdown}>
					<DropdownTrigger>
						<Button size="sm" variant="flat" color="primary" startContent={<BiPlus />}>
							Add New Field
						</Button>
					</DropdownTrigger>
					<DropdownMenu aria-label="Add field options">

						{(() => {
							// Create items for field options
							const fieldItems = [
								{ key: "email", label: "Email" },
								{ key: "phone", label: "Phone" },
								{ key: "address", label: "Address" },
								{ key: "company", label: "Company" },
								{ key: "position", label: "Position" },
								{ key: "language", label: "Language" },
								{ key: "vatId", label: "VAT ID" },
								{ key: "birthdate", label: "Birthdate" },
							]
								.filter((field) => !visibleFields.has(field.key as FieldType))
								.map((field) => (
									<DropdownItem
										key={field.key}
										onPress={() => handleAddField(field.key as FieldType)}
									>
										{field.label}
									</DropdownItem>
								));

							// Add custom field item
							fieldItems.push(
								<DropdownItem key="customField">
									<div className="flex items-center gap-2">
										<Input
											size="sm"
											placeholder="Custom field name"
											value={customFieldName}
											onValueChange={setCustomFieldName}
											onPointerDown={(e) => e.stopPropagation()}
										/>
										<Button
											size="sm"
											color="primary"
											isDisabled={!customFieldName.trim()}
											onPress={handleAddCustomField}
											onPointerDown={(e) => e.stopPropagation()}
										>
											Add
										</Button>
									</div>
								</DropdownItem>
							);

							return fieldItems;
						})()}
					</DropdownMenu>
				</Dropdown>
			</div>*/}
		</form>
	);
};

export default CustomerForm;
