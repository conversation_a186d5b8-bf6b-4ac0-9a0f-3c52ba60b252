import { UseFormRegister } from "react-hook-form";
import { LookBookFormData } from "../../validation/lookbookValidationSchema";

interface LookBookFileInputProps {
	register: UseFormRegister<LookBookFormData>;
	Icon: React.ComponentType<any>;
	titleText: string;
	name: keyof LookBookFormData;
	accept: string;
	maxFileSizeMB?: number;
}

interface LookBookFileInput2Props {
	handleFileChange: (name: keyof LookBookFormData, files: FileList) => void;
	Icon: React.ComponentType<any>;
	titleText: string;
	name: keyof LookBookFormData;
	accept: string;
	maxFileSizeMB?: number;
}

export const LookBookFileInput: React.FC<LookBookFileInputProps> = ({
	register,
	Icon,
	titleText,
	name,
	accept,
	maxFileSizeMB = 5,
}) => {
	return (
		<div className="bg-lightPrimary h-40 w-full rounded-md flex flex-col justify-center gap-y-2 p-3">
			<Icon className="text-4xl text-primary mx-auto" />
			<label htmlFor={name} className="text-center text-sm font-medium text-primary cursor-pointer">
				{titleText}
			</label>
			<input
				id={name}
				type="file"
				multiple
				accept={accept}
				className="hidden"
				{...register(name)}
			/>
			<p className="text-xs text-gray-500 text-center">Max size: {maxFileSizeMB}MB</p>
		</div>
	);
};

export const LookBookFileInput2: React.FC<LookBookFileInput2Props> = ({
	handleFileChange,
	Icon,
	titleText,
	name,
	accept,
	maxFileSizeMB = 5,
}) => {
	return (
		<div className="flex flex-col gap-y-1">
			<Icon className="text-lg text-primary mx-auto" />
			<label
				htmlFor={`${name}-add`}
				className="text-center text-xs font-medium text-primary cursor-pointer"
			>
				{titleText}
			</label>
			<input
				id={`${name}-add`}
				type="file"
				multiple
				accept={accept}
				className="hidden"
				onChange={(e) => {
					if (e.target.files) {
						handleFileChange(name, e.target.files);
					}
				}}
			/>
			<p className="text-xs text-gray-400 text-center">Max: {maxFileSizeMB}MB</p>
		</div>
	);
};
