import { useEffect, useRef, useState } from "react";

interface VideoPreviewProps {
	src: string;
}

const VideoPreview = ({ src }: VideoPreviewProps) => {
	const [playing, setPlaying] = useState(false);
	const videoRef = useRef<HTMLVideoElement | null>(null);

	useEffect(() => {
		if (playing && videoRef.current) {
			videoRef.current.play();
		} else if (!playing && videoRef.current) {
			videoRef.current.pause();
			videoRef.current.currentTime = 0;
		}
	}, [playing]);

	return (
		<div className="relative hover:cursor-pointer w-36 h-[9.4rem] rounded-md border">
			<video
				ref={videoRef}
				src={src}
				muted
				onMouseEnter={() => setPlaying(true)}
				onMouseLeave={() => setPlaying(false)}
				className="w-full h-full rounded-md object-cover"
				onEnded={() => setPlaying(false)}
			/>
		</div>
	);
};

export default VideoPreview;
