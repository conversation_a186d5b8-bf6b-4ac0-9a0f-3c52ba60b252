import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import { LookBookType } from "../../types/lookbookType";
import { StatusTypes } from "../../types/commonTypes";

const INITIAL_VISIBLE_COLUMNS = [
  "name",
  "status",
  "totalPrice",
  "products",
  "sections",
  "actions",
];

export interface LookbookSlice {
  lookbookVisibleColumns: Set<string>;
  selectedLookbookKeys: Set<string> | "all";
  deleteLookbookId: string | null;
  lookbookFilterText: string;
  lookbookFilters: {
    status: StatusTypes | null;
    search: string | null;
  };
  setLookbookVisibleColumns: (value: Set<string>) => void;
  setSelectedLookbookKeys: (value: Set<string> | "all") => void;
  setDeleteLookbookId: (value: string | null) => void;
  setLookbookFilterText: (value: string) => void;
  setLookbookFilters: (value: {
    status?: StatusTypes | null;
    search?: string | null;
  }) => void;
}

const initialState: Omit<
  LookbookSlice,
  | "setLookbookVisibleColumns"
  | "setSelectedLookbookKeys"
  | "setDeleteLookbookId"
  | "setLookbookFilterText"
  | "setLookbookFilters"
> = {
  lookbookVisibleColumns: new Set(INITIAL_VISIBLE_COLUMNS),
  selectedLookbookKeys: new Set([]),
  deleteLookbookId: null,
  lookbookFilterText: "",
  lookbookFilters: {
    status: null,
    search: null,
  },
};

export const lookbookSlice: StateCreator<
  LookbookSlice,
  [],
  [["zustand/immer", never]]
> = immer((set) => ({
  ...initialState,
  setLookbookVisibleColumns: (value: Set<string>) =>
    set((state) => {
      state.lookbookVisibleColumns = value;
    }),
  setSelectedLookbookKeys: (value) =>
    set((state) => {
      state.selectedLookbookKeys = value;
    }),
  setDeleteLookbookId: (value) =>
    set((state) => {
      state.deleteLookbookId = value;
    }),
  setLookbookFilterText: (value) =>
    set((state) => {
      state.lookbookFilterText = value;
    }),
  setLookbookFilters: (value: Partial<typeof initialState.lookbookFilters>) =>
    set((state) => {
      state.lookbookFilters = {
        ...state.lookbookFilters,
        ...value,
      };
    }),
}));