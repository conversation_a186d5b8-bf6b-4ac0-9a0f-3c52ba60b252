import React from "react";
import {
	UseFormRegister,
	UseFormSetValue,
	UseFormWatch,
	FieldErrors,
	Control,
	Controller,
} from "react-hook-form";
import { Input, Select, SelectItem, NumberInput } from "@heroui/react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";

import { LookBookFormData } from "../../validation/lookbookValidationSchema";
import { StatusTypes } from "../../types/commonTypes";
import LookBookTopContent from "./LookBookTopContent";

interface LookBookStep1Props {
	register: UseFormRegister<LookBookFormData>;
	errors: FieldErrors<LookBookFormData>;
	watch: UseFormWatch<LookBookFormData>;
	setFormValue: UseFormSetValue<LookBookFormData>;
	control: Control<LookBookFormData>;
}

const statusOptions = [
	{ key: StatusTypes.DRAFT, label: "Draft" },
	{ key: StatusTypes.PUBLISHED, label: "Published" },
	{ key: StatusTypes.SCHEDULED, label: "Scheduled" },
	{ key: StatusTypes.ARCHIVED, label: "Archived" },
];

const LookBookStep1: React.FC<LookBookStep1Props> = ({
	register,
	errors,
	watch,
	setFormValue,
	control,
}) => {
	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-xl font-semibold mb-4">Basic Details</h2>
				<p className="text-gray-600 dark:text-gray-400 mb-6">
					Add basic information and media for your lookbook
				</p>
			</div>

			{/* Media Upload Section */}
			<div className="space-y-4">
				<h3 className="text-lg font-medium">Media</h3>
				<LookBookTopContent
					register={register}
					errors={errors}
					watch={watch}
					setFormValue={setFormValue}
				/>
			</div>

			{/* Basic Information */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Name */}
				<div className="md:col-span-2">
					<Controller
						control={control}
						name="name"
						render={({ field }) => (
							<Input
								label="Title"
								placeholder="Enter lookbook title"
								isRequired
								{...field}
								errorMessage={errors.name?.message}
								isInvalid={!!errors.name}
								labelPlacement="outside"
								size="lg"
								classNames={{
									inputWrapper: "rounded-md",
									label: "text-sm font-medium",
								}}
							/>
						)}
					/>
				</div>
				<div className="md:col-span-2">
					<Controller
						control={control}
						name="subName"
						render={({ field }) => (
							<Input
								label="Subheading"
								placeholder="Enter lookbook subheading"
								isRequired
								{...field}
								errorMessage={errors.subName?.message}
								isInvalid={!!errors.subName}
								labelPlacement="outside"
								size="lg"
								classNames={{
									inputWrapper: "rounded-md",
									label: "text-sm font-medium",
								}}
							/>
						)}
					/>
				</div>

				{/* Description */}
				<div className="md:col-span-2">
					<label className="block text-sm font-medium mb-2">
						Description <span className="text-danger">*</span>
					</label>
					<Controller
						control={control}
						name="description"
						render={({ field }) => (
							<ReactQuill
								theme="snow"
								value={field.value || ""}
								onChange={field.onChange}
								placeholder="Enter lookbook description..."
								style={{ height: "150px", marginBottom: "50px" }}
								modules={{
									toolbar: [
										[{ header: [1, 2, 3, false] }],
										["bold", "italic", "underline", "strike"],
										[{ list: "ordered" }, { list: "bullet" }],
										["link"],
										["clean"],
									],
								}}
							/>
						)}
					/>
					{errors.description && (
						<small className="text-danger">{errors.description.message}</small>
					)}
				</div>

				{/* Total Price */}
				{/* <div>
					<Controller
						control={control}
						name="totalPrice"
						render={({ field }) => (
							<NumberInput
								label="Total Price"
								placeholder="0.00"
								startContent={
									<div className="pointer-events-none flex items-center">
										<span className="text-default-400 text-small">₹</span>
									</div>
								}
								value={field.value}
								onValueChange={field.onChange}
								labelPlacement="outside"
								size="lg"
								classNames={{
									inputWrapper: "rounded-md",
									label: "text-sm font-medium",
								}}
								errorMessage={errors.totalPrice?.message}
								isInvalid={!!errors.totalPrice}
							/>
						)}
					/>
				</div> */}

				{/* Status */}
				<div className="md:col-span-2">
					<Controller
						control={control}
						name="status"
						render={({ field }) => (
							<Select
								label="Status"
								placeholder="Select status"
								isRequired
								selectedKeys={field.value ? [field.value] : []}
								onSelectionChange={(keys) => {
									const selectedKey = Array.from(keys)[0] as StatusTypes;
									field.onChange(selectedKey);
								}}
								labelPlacement="outside"
								size="lg"
								classNames={{
									trigger: "rounded-md",
									label: "text-sm font-medium",
								}}
								errorMessage={errors.status?.message}
								isInvalid={!!errors.status}
							>
								{statusOptions.map((status) => (
									<SelectItem key={status.key}>{status.label}</SelectItem>
								))}
							</Select>
						)}
					/>
				</div>
			</div>

			{/* Help Text */}
			<div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
				<h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
					Tips for creating a great lookbook:
				</h4>
				<ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
					<li>• Use high-quality images that showcase your products</li>
					<li>• Write a compelling description that tells a story</li>
					<li>• Set an appropriate total price if you're selling as a bundle</li>
					<li>• Choose the right status based on your publishing timeline</li>
				</ul>
			</div>
		</div>
	);
};

export default LookBookStep1;
