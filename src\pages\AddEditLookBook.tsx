import { useEffect, useState } from "react";
import { addToast, BreadcrumbItem, Breadcrum<PERSON>, Button, Form } from "@heroui/react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "react-router-dom";
import { useMutation, useQuery } from "@apollo/client";
import { v4 as uuid } from "uuid";

import Stepper from "../components/Stepper";
import CardWrapper from "../components/CardWrapper";
import { StatusTypes } from "../types/commonTypes";
import { PUT_FILE } from "../graphql/files";
import {
	CREATE_LOOKBOOK,
	UPDATE_LOOKBOOK,
	GET_LOOKBOOK_BY_ID,
	GET_LOOKBOOKS,
} from "../graphql/lookbook";
import { lookbookValidationSchema, LookBookFormData } from "../validation/lookbookValidationSchema";

// Import step components (we'll create these next)
import LookBookStep1 from "../components/lookbook/LookBookStep1";
import LookBookStep2 from "../components/lookbook/LookBookStep2";
import LookBookStep3 from "../components/lookbook/LookBookStep3";
import { formateFolderName } from "../helpers/formatters";

type PropsTypes = {
	method?: string;
};

const steps = [
	{ id: 1, title: "Basic Details", description: "Name, description, assets" },
	{ id: 2, title: "Sections", description: "Add content sections" },
	{ id: 3, title: "Products", description: "Select products" },
];

const AddEditLookBook = ({ method }: PropsTypes) => {
	const { id } = useParams();
	const navigate = useNavigate();
	const [currentStep, setCurrentStep] = useState(1);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const isEditMode = method === "PUT" && id;

	const commonRefetch = {
		query: GET_LOOKBOOKS,
		variables: { limit: 10, offset: 0, filter: { search: null } },
	};

	const [createLookBook, { loading: createLoading, error: createError, data: createData }] =
		useMutation(CREATE_LOOKBOOK, {
			refetchQueries: [commonRefetch],
			awaitRefetchQueries: true,
			update(cache) {
				cache.evict({ fieldName: "getLookBooks" });
				cache.gc();
			},
		});
	const [updateLookBook, { loading: updateLoading, error: updateError, data: updateData }] =
		useMutation(UPDATE_LOOKBOOK, {
			refetchQueries: [commonRefetch],
			awaitRefetchQueries: true,
			update(cache) {
				cache.evict({ fieldName: "getLookBooks" });
				cache.gc();
			},
		});

	const [getSignedUrlToPutObject] = useMutation(PUT_FILE);

	const { data: lookBookData, loading: fetchLoading } = useQuery(GET_LOOKBOOK_BY_ID, {
		variables: { getLookBookByIdId: id },
		skip: !isEditMode,
	});

	// Form setup
	const {
		register,
		formState: { errors },
		watch,
		setValue: setFormValue,
		reset,
		control,
		handleSubmit,
		trigger,
	} = useForm<LookBookFormData>({
		resolver: zodResolver(lookbookValidationSchema),
		defaultValues: {
			assets: [],
			name: "",
			subName: "",
			description: "",
			status: StatusTypes.DRAFT,
			images: new DataTransfer().files,
			videos: new DataTransfer().files,
			sections: [
				{
					id: uuid(),
					imageUrl: "",
					videourl: "",
					paragraph: "",
				},
			],
			productIds: [],
		},
	});

	// Load data for edit mode
	useEffect(() => {
		if (isEditMode && lookBookData?.getLookBookById) {
			const lookBook = lookBookData.getLookBookById;
			reset({
				name: lookBook.name || "",
				subName: lookBook.subName || "",
				description: lookBook.description || "",
				// totalPrice: lookBook.totalPrice || 0,
				status: lookBook.status || StatusTypes.DRAFT,
				sections: lookBook.sections?.map((section: any) => ({
					id: uuid(),
					imageUrl: section.imageUrl || "",
					videourl: section.videourl || "",
					paragraph: section.paragraph || "",
				})),
				productIds: lookBook.productIds || [],
				assets: lookBook.assets || [],
				images: new DataTransfer().files,
				videos: new DataTransfer().files,
			});
		}
	}, [lookBookData, isEditMode, reset]);

	const handleNext = async () => {
		let isValid = false;

		if (currentStep === 1) {
			isValid = await trigger(["name", "subName", "description", "status", "images", "videos", "assets"]);
		} else if (currentStep === 2) {
			isValid = await trigger(["sections"]);
		}

		if (isValid) {
			setCurrentStep((prev) => Math.min(prev + 1, steps.length));
		} else {
			addToast({
				title: "Please fix the errors before proceeding",
				color: "warning",
			});
		}
	};

	const handlePrevious = () => {
		setCurrentStep((prev) => Math.max(prev - 1, 1));
	};

	const handleCancel = () => {
		navigate("/admin/store/lookbook");
	};

	const onSubmit: SubmitHandler<LookBookFormData> = async (data) => {
		setIsSubmitting(true);
		try {
			// Validate that we have at least one media file or existing asset
			const hasImages = data.images && data.images.length > 0;
			const hasVideos = data.videos && data.videos.length > 0;
			const hasAssets = data.assets && data.assets.length > 0;
			const folderName = formateFolderName(data.name);
			
			if (!hasImages && !hasVideos && !hasAssets) {
				addToast({
					title: "Please add at least one image or video",
					color: "warning",
				});
				setIsSubmitting(false);
				return;
			}

			const { images, videos, assets, sections, ...rest } = data;

			// Convert FileList to Array
			const filesArray = [...(images ?? []), ...(videos ?? [])];

			// Prepare filesData payload for main assets
			const filesData = filesArray.map((file) => ({
				fileName: file.name,
				extension: file.name.split(".").pop(),
				contentType: file.type,
				fileKey: `lookbooks/${folderName}-${Date.now()}/${file.name}`,
			}));

			// Collect section files
			const sectionFiles: File[] = [];
			const sectionFilesData: any[] = [];

			sections?.forEach((section, index) => {
				if (section.imageFile) {
					sectionFiles.push(section.imageFile);
					sectionFilesData.push({
						fileName: section.imageFile.name,
						extension: section.imageFile.name.split(".").pop(),
						contentType: section.imageFile.type,
						fileKey: `lookbooks/${folderName}-${Date.now()}/sections/${index}-${
							section.imageFile.name
						}`,
					});
				}
				if (section.videoFile) {
					sectionFiles.push(section.videoFile);
					sectionFilesData.push({
						fileName: section.videoFile.name,
						extension: section.videoFile.name.split(".").pop(),
						contentType: section.videoFile.type,
						fileKey: `lookbooks/${folderName}-${Date.now()}/sections/${index}-${
							section.videoFile.name
						}`,
					});
				}
			});

			// Combine all files for upload
			const allFilesData = [...filesData, ...sectionFilesData];
			const allFiles = [...filesArray, ...sectionFiles];

			let uploadedAssets: any[] = [];
			let uploadedSectionUrls: { [key: string]: string } = {};

			if (allFilesData.length > 0) {
				const { data: signedUrlsResponse } = await getSignedUrlToPutObject({
					variables: { filesData: allFilesData },
				});

				if (!signedUrlsResponse || !signedUrlsResponse.getSignedUrlToPutObject) {
					throw new Error("Failed to get signed URLs");
				}

				// Upload all files
				const uploadedFiles = await Promise.all(
					signedUrlsResponse.getSignedUrlToPutObject.map(
						async (fileInfo: { url: string; cdnUrl: string }, index: number) => {
							try {
								const response = await fetch(fileInfo.url, {
									method: "PUT",
									body: allFiles[index],
									headers: { "Content-Type": allFiles[index].type },
								});

								if (!response.ok) throw new Error(`Upload failed for ${allFiles[index].name}`);

								return {
									altText: allFiles[index].name,
									isFeatured: false,
									path: fileInfo.cdnUrl,
									type: allFiles[index].type.includes("image") ? "IMAGE" : "VIDEO",
								};
							} catch (error) {
								console.error("Upload error:", error);
								throw error;
							}
						}
					)
				);

				// Separate main assets from section assets
				uploadedAssets = uploadedFiles.slice(0, filesArray.length);
				const sectionUploadedFiles = uploadedFiles.slice(filesArray.length);

				// Map section files back to their sections
				let sectionFileIndex = 0;
				sections?.forEach((section, index) => {
					if (section.imageFile) {
						uploadedSectionUrls[`${index}-image`] = sectionUploadedFiles[sectionFileIndex].path;
						sectionFileIndex++;
					}
					if (section.videoFile) {
						uploadedSectionUrls[`${index}-video`] = sectionUploadedFiles[sectionFileIndex].path;
						sectionFileIndex++;
					}
				});
			}

			// Prepare final sections data
			const finalSections = sections?.map((section, index) => ({
				imageUrl: uploadedSectionUrls[`${index}-image`] || section.imageUrl || null,
				videourl: uploadedSectionUrls[`${index}-video`] || section.videourl || null,
				paragraph: section.paragraph || null,
			}));

			// Combine existing assets with new uploads
			const finalAssets = [...(assets || []), ...uploadedAssets];

			const input = {
				...rest,
				assets: finalAssets,
				sections: finalSections,
			};

			if (isEditMode) {
				await updateLookBook({
					variables: {
						updateLookBookId: id,
						input,
					},
				});
				addToast({
					title: "LookBook updated successfully!",
					color: "success",
				});
			} else {
				await createLookBook({
					variables: { input },
				});
				addToast({
					title: "LookBook created successfully!",
					color: "success",
				});
			}

			navigate("/admin/store/lookbook");
		} catch (error) {
			console.error("Error saving lookbook:", error);
			addToast({
				title: "Error saving lookbook",
				color: "danger",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSaveClick = () => {
		handleSubmit(onSubmit)();
	};

	if (fetchLoading) {
		return <div>Loading...</div>;
	}

	return (
		<>
			{/* Header with breadcrumbs and buttons */}
			<div className="grid grid-cols-1 py-2 md:grid-cols-2 justify-end items-center border-b dark:border-slate-700 mb-5 ml-1 sticky top-[3.15rem] bg-white dark:bg-slate-900 left-0 z-30">
				<div className="px-5">
					<Breadcrumbs>
						<BreadcrumbItem>Home</BreadcrumbItem>
						<BreadcrumbItem>LookBook</BreadcrumbItem>
						<BreadcrumbItem>{isEditMode ? "Edit" : "Create"}</BreadcrumbItem>
					</Breadcrumbs>
				</div>
				<div className="flex gap-x-4 w-full justify-end">
					{currentStep > 1 ? (
						<Button size="sm" radius="full" color="primary" variant="ghost" onPress={handlePrevious}>
							← Previous
						</Button>
					) : (
						<Button size="sm" radius="full" color="primary" variant="ghost" onPress={handleCancel}>
							Cancel
						</Button>
					)}
					{currentStep < steps.length ? (
						<Button size="sm" radius="full" color="primary" onPress={handleNext}>
							Next
						</Button>
					) : (
						<Button
							size="sm"
							radius="full"
							color="primary"
							isLoading={createLoading || updateLoading || isSubmitting}
							onPress={handleSaveClick}
						>
							{isEditMode ? "Update" : "Save"}
						</Button>
					)}
				</div>
			</div>

			{/* Stepper */}
			<div className="mb-8">
				<Stepper steps={steps} currentStep={currentStep} />
			</div>

			{/* Step Navigation */}
			{/* {currentStep > 1 && (
				<div className="mb-6">
					<Button size="sm" variant="ghost" onPress={handlePrevious}>
						← Previous
					</Button>
				</div>
			)} */}

			{/* Form Steps */}
			<Form onSubmit={handleSubmit(onSubmit)}>
				<CardWrapper>
					{currentStep === 1 && (
						<LookBookStep1
							register={register}
							errors={errors}
							watch={watch}
							setFormValue={setFormValue}
							control={control}
						/>
					)}
					{currentStep === 2 && (
						<LookBookStep2
							errors={errors}
							watch={watch}
							setFormValue={setFormValue}
							control={control}
						/>
					)}
					{currentStep === 3 && (
						<LookBookStep3 errors={errors} watch={watch} setFormValue={setFormValue} />
					)}
				</CardWrapper>
			</Form>
		</>
	);
};

export default AddEditLookBook;
