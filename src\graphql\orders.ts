import { gql } from "@apollo/client";

export const FETCH_ORDERS = gql`
	query GetOrdersByFilters($filter: OrderFilterInput, $offset: Int, $limit: Int) {
		getOrdersByFilters(filter: $filter, offset: $offset, limit: $limit) {
			data {
				_id
				tagIds
				orderNo
				orderPrice
				paymentStatus
				currency
				fulfillmentStatus
				userData {
					firstName
					lastName
					userId
				}
				itemcount
				totalprice
				createdAt
			}
			totalCount
		}
	}
`;
export const FETCH_ORDER_BY_ID = gql`
	query GetOrderById($getOrderByIdId: ID!) {
		getOrderById(id: $getOrderByIdId) {
			_id
			billingAddress {
				addressline1
				addressline2
				addressType
				city
				country
				flat
				landmark
				phone
				states
				pincode
				primary
			}
			billingAddressId
			coupon {
				couponCode
				couponName
				couponId
			}
			createdAt
			customDiscount {
				amount
				discountReason
			}
			customFee {
				amount
				name
			}
			discountedPrice
			fulfillmentStatus
			itemcount
			orderNo
			orderPrice
			paymentStatus
			shippingAddress {
				addressType
				flat
				addressline1
				addressline2
				landmark
				phone
				city
				country
				states
				pincode
				primary
			}
			shippingAddressId
			shippingAmount
			tagIds
			taxAmount
			totalDiscount
			totalprice
			userData {
				firstName
				lastName
				email
				phone
			}
			cart {
				asset {
					path
					_id
					altText
					type
				}
				finalPrice
				name
				price
				productId
				qty
			}
		}
	}
`;

export const CREATE_ADMIN_ORDER = gql`
	mutation CreateAdminOrder($input: CreateAdminOrderInput!) {
		createAdminOrder(input: $input) {
			data {
				_id
				cart {
					name
					qty
					price
					variantDetail {
						variantDetailId
						selectedOptions
						priceDifference
						variantPrice
						variantCostOfGoods
						shippingWeight
						sku
						trackInventory
						stockQuantity
						stockStatus
						status
					}
					finalPrice
					customTexts
					productId
					categoryId
				}
				createdAt
				currency
				orderNo
				orderPrice
				billingAddressId
				shippingAddressId
				seenByHuman
				paymentStatus
				fulfillmentStatus
				shippingAmount
				taxAmount
				itemcount
				totalprice
				discountedPrice
				totalDiscount
				nimbblOrderId
				nimbblInvoiceId
				nimbblTransanctionId
				nimbblUserId
				updatedAt
				shippingAddress {
					addressType
					flat
					addressline1
					addressline2
					landmark
					phone
					city
					country
					states
					pincode
					primary
				}
				customFee {
					name
					amount
				}
				userData {
					userId
					firstName
					lastName
					email
					phone
					countryCode
				}
			}
			nimbblData {
				order_date
				order_id
				status
				invoice_id
				attempts
				currency
				amount_before_tax
				tax
				total_amount
				token
				token_expiration
				refresh_token
				refresh_token_expiration
				user {
					user_id
					token
					mobile_number
					token_expiration
					last_name
					first_name
					email
					country_code
				}
			}
		}
	}
`;
