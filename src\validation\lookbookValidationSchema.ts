import * as z from "zod";
import { StatusTypes } from "../types/commonTypes";

export const lookbookValidationSchema = z
	.object({
		// Step 1: Basic Details
		images: z.instanceof(FileList).optional(),
		videos: z.instanceof(FileList).optional(),
		assets: z
			.array(
				z.object({
					altText: z.string().nullable().optional(),
					isFeatured: z.boolean().nullable().optional(),
					path: z.string().nullable().optional(),
					type: z.enum(["IMAGE", "VIDEO"]).nullable().optional(),
				})
			)
			.optional(),
		name: z.string().min(1, { message: "Title is required" }),
		subName: z.string().min(1, { message: "Subheading is required" }),
		description: z.string().min(1, { message: "Description is required" }),
		// totalPrice: z.number().min(0, { message: "Total price must be a positive number" }).optional(),
		status: z.nativeEnum(StatusTypes, { message: "Status is required" }),

		// Step 2: Sections
		sections: z
			.array(
				z.object({
					id: z.string(),
					imageUrl: z.string().optional(),
					videourl: z.string().optional(),
					paragraph: z.string().optional(),
					imageFile: z.instanceof(File).optional(),
					videoFile: z.instanceof(File).optional(),
				})
			)
			.min(1, { message: "At least one section is required" }),

		// Step 3: Product Selection
		productIds: z.array(z.string()).min(1, { message: "At least one product must be selected" }),
	})
	.refine(
		(data) => {
			// Check if there are any media files or existing assets
			const hasImages = data.images && data.images.length > 0;
			const hasVideos = data.videos && data.videos.length > 0;
			const hasAssets = data.assets && data.assets.length > 0;

			return hasImages || hasVideos || hasAssets;
		},
		{
			message: "At least one image or video is required",
			path: ["images"], // This will show the error on the images field
		}
	);

// Individual step validation schemas for better UX
export const step1ValidationSchema = z
	.object({
		name: z.string().min(1, { message: "Name is required" }),
		description: z.string().min(1, { message: "Description is required" }),
		// totalPrice: z.number().min(0, { message: "Total price must be a positive number" }).optional(),
		status: z.nativeEnum(StatusTypes, { message: "Status is required" }),
		images: z.instanceof(FileList).optional(),
		videos: z.instanceof(FileList).optional(),
		assets: z
			.array(
				z.object({
					altText: z.string().nullable().optional(),
					isFeatured: z.boolean().nullable().optional(),
					path: z.string().nullable().optional(),
					type: z.enum(["IMAGE", "VIDEO"]).nullable().optional(),
				})
			)
			.optional(),
	})
	.refine(
		(data) => {
			// Check if there are any media files or existing assets
			const hasImages = data.images && data.images.length > 0;
			const hasVideos = data.videos && data.videos.length > 0;
			const hasAssets = data.assets && data.assets.length > 0;

			return hasImages || hasVideos || hasAssets;
		},
		{
			message: "At least one image or video is required",
			path: ["images"],
		}
	);

export const step2ValidationSchema = z.object({
	sections: z
		.array(
			z.object({
				id: z.string(),
				imageUrl: z.string().optional(),
				videourl: z.string().optional(),
				paragraph: z.string().optional(),
				imageFile: z.instanceof(File).optional(),
				videoFile: z.instanceof(File).optional(),
			})
		)
		.min(1, { message: "At least one section is required" }),
});

export const step3ValidationSchema = z.object({
	productIds: z.array(z.string()).min(1, { message: "At least one product must be selected" }),
});

export type LookBookFormData = z.infer<typeof lookbookValidationSchema>;
export type Step1FormData = z.infer<typeof step1ValidationSchema>;
export type Step2FormData = z.infer<typeof step2ValidationSchema>;
export type Step3FormData = z.infer<typeof step3ValidationSchema>;
