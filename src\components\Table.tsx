import {
	<PERSON>,
	<PERSON><PERSON><PERSON>er,
	TableColumn,
	TableBody,
	TableRow,
	Table<PERSON>ell,
	Spinner,
} from "@heroui/react";
import { useInfiniteScroll } from "@heroui/use-infinite-scroll";
import { memo, useMemo } from "react";

interface ColumnType {
	name: string;
	uid: string;
	sortable?: boolean;
}

interface StatusType {
	name: string;
	uid: string;
}

interface TableComponentProps<T extends { _id: string }> {
	columns: ColumnType[]; // columns for the table
	statusOptions?: StatusType[]; // status options for th table cell
	renderCell: (item: T, columnKey: keyof T | string) => React.ReactNode; //content to be rendered in each table row
	topContent?: React.ReactNode; // top content on the table includes the buttons and filters
	topSelectedContent?: React.ReactNode; // top content on the table when the row is selected
	list: { items: T[] }; // async list data for the table
	visibleColumns: Set<string>; // all the visible columns for the table
	isLoading: boolean; // is loading
	hasMore: boolean; // if there is any more amount of data
	selectedKeys?: Set<string> | "all"; //
	setSelectedKeys?: (keys: Set<string>) => void;
	handleLoadMore: () => void;
	tableClassName?: string; // custom class name for the table
}

const TableComponent = <T extends { _id: string }>({
	columns = [],
	renderCell,
	topContent = null,
	topSelectedContent = null,
	list,
	visibleColumns = new Set([]),
	isLoading = false,
	hasMore = false,
	selectedKeys,
	setSelectedKeys = () => {},
	handleLoadMore = () => {},
	tableClassName = "mainTable", // Default to mainTable if not provided
}: TableComponentProps<T>) => {
	// Use useInfiniteScroll hook with proper dependencies
	const [loaderRef, scrollerRef] = useInfiniteScroll({
		hasMore,
		onLoadMore: handleLoadMore,
	});

	const headerColumns = useMemo(() => {
		if (visibleColumns.has("all")) return columns;

		return columns.filter((column) => Array.from(visibleColumns).includes(column.uid));
	}, [visibleColumns, columns]);

	return (
		<>
			<Table
				isHeaderSticky
				aria-label="Example table with infinite pagination"
				baseRef={scrollerRef}
				className={`${tableClassName} gap-0 border-1 border-gray-200 dark:border-slate-600 rounded-lg`}
				bottomContent={
					hasMore ? (
						<div className="flex w-full justify-center">
							<Spinner color="primary" ref={loaderRef} />
						</div>
					) : null
				}
				classNames={{
					base: "max-h-[600px] overflow-auto",
					table: "min-h-[100px]",
					wrapper: "scrollbar p-0 shadow-none rounded-none mt-0",
					thead: "!rounded-none",
					th: "bg-[#c3e5ff] dark:bg-black py-3 !rounded-none font-semibold text-primaryBlack text-sm",
					tr: "hover:bg-lightPrimary dark:hover:bg-slate-600",
				}}
				selectedKeys={selectedKeys}
				onRowAction={() => {
					return;
				}}
				selectionBehavior="toggle"
				selectionMode="multiple"
				topContent={
					(selectedKeys instanceof Set && selectedKeys.size > 0) || selectedKeys === "all"
						? topSelectedContent
						: topContent
				}
				topContentPlacement="outside"
				onSelectionChange={(keys) => {
					setSelectedKeys(keys as Set<string>);
				}}
			>
				<TableHeader columns={headerColumns}>
					{(column) => (
						<TableColumn
							key={column.uid}
							align={column.uid === "actions" ? "center" : "start"}
							allowsSorting={column.sortable}
						>
							{column.name}
						</TableColumn>
					)}
				</TableHeader>
				<TableBody
					isLoading={isLoading && list.items.length === 0}
					items={list.items}
					emptyContent={"No items found"}
					loadingContent={<Spinner color="primary" />}
				>
					{(item) => (
						<TableRow key={item._id}>
							{(columnKey) => (
								<TableCell>{renderCell(item, columnKey as string) ?? null}</TableCell>
							)}
						</TableRow>
					)}
				</TableBody>
			</Table>
		</>
	);
};

const MemoizedTableComponent = memo(TableComponent) as typeof TableComponent;
export default MemoizedTableComponent;
