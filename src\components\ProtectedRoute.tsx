import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useBoundStore } from "../store/store";
import { addToast } from "@heroui/react";

import { ReactNode } from "react";

interface ProtectedRouteProps {
	children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
	const navigate = useNavigate();
	const user = useBoundStore((state) => state.user);
	useEffect(() => {
		if (!user) {
			navigate("/sign-in");
			addToast({
				title: "Session Expired! Please log in again",
				color: "warning",
			});
		}
	}, [user]);
	return <>{children}</>;
};

export default ProtectedRoute;
