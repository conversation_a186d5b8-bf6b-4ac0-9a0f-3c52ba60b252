import React, { useEffect, useRef, useState, useCallback, memo } from "react";
import { UseFormSetValue, UseFormWatch, FieldErrors } from "react-hook-form";
import { Input, Spinner, CheckboxGroup, Chip } from "@heroui/react";
import { useQuery } from "@apollo/client";
import { BiSearch } from "react-icons/bi";

import { LookBookFormData } from "../../validation/lookbookValidationSchema";
import { GET_PRODUCTS } from "../../graphql/products";
import { GetProductsType } from "../../types/productType";
import { useInfiniteQueryScroll } from "../../hooks/useInfiniteQueryScroll";
import useDebouncedInput from "../../hooks/useDebouncedInput";
import ProductCheckbox from "./ProductCheckbox";

// Memoize ProductCheckbox to prevent unnecessary re-renders
const MemoizedProductCheckbox = memo(ProductCheckbox);

interface LookBookStep3Props {
	errors: FieldErrors<LookBookFormData>;
	watch: UseFormWatch<LookBookFormData>;
	setFormValue: UseFormSetValue<LookBookFormData>;
}

const LookBookStep3: React.FC<LookBookStep3Props> = ({ errors, watch, setFormValue }) => {
	const [filterValue, setFilterValue] = useState<string>("");
	const inputRef = useRef<HTMLInputElement>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	const selectedProductIds = watch("productIds") || [];

	const { searchValue, handleSearchChange, setSearchValue } = useDebouncedInput({
		initialValue: "",
		delay: 400,
		onChange: (value) => {
			setFilterValue(value);
		},
	});

	const { data, loading, error, fetchMore } = useQuery(GET_PRODUCTS, {
		variables: {
			offset: 0,
			limit: 20,
			filters: {
				search: filterValue || undefined,
			},
		},
		notifyOnNetworkStatusChange: true,
	});

	// Remove the circular dependency between filterValue and searchValue

	// Infinite scroll
	const { observerRef } = useInfiniteQueryScroll({
		items: data?.getProducts?.products || [],
		totalCount: data?.getProducts?.totalCount || 0,
		loading,
		fetchMore,
		scrollContainerRef,
	});

	// Handle selection change - memoized to prevent unnecessary re-renders
	const handleSelectionChange = useCallback((values: string[]) => {
		setFormValue("productIds", values);
	}, [setFormValue]);

	// Memoize products to prevent unnecessary re-renders
	const products = React.useMemo(() => data?.getProducts?.products || [], [data?.getProducts?.products]);

	const handleClear = useCallback(() => {
		setSearchValue("");
	}, [setSearchValue]);

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-xl font-semibold mb-4">Select Products</h2>
				<p className="text-gray-600 dark:text-gray-400 mb-6">
					Choose the products you want to feature in this lookbook. You can select multiple
					products.
				</p>
			</div>

			{/* Search */}
			<Input
				ref={inputRef}
				isClearable
				size="sm"
				classNames={{
					base: "w-full",
					inputWrapper: "rounded-md",
				}}
				placeholder="Search products..."
				startContent={<BiSearch />}
				value={searchValue}
				onClear={handleClear}
				onValueChange={handleSearchChange}
			/>
			{/* Selected Products Summary */}
			{selectedProductIds.length > 0 && (
				<div className="mt-4">
					<p className="font-medium mb-2">Selected Products ({selectedProductIds.length})</p>
					<div className="flex flex-wrap gap-2">
						{selectedProductIds.map((productId) => {
							const product = products.find((p: GetProductsType) => p._id === productId);
							return (
								<Chip
									key={productId}
									size="sm"
									variant="flat"
									color="primary"
									onClose={() => {
										setFormValue(
											"productIds",
											selectedProductIds.filter((id) => id !== productId)
										);
									}}
								>
									{product?.name || productId}
								</Chip>
							);
						})}
					</div>
				</div>
			)}

			{/* Products List with Infinite Scroll */}
			<div ref={scrollContainerRef} className="max-h-96 overflow-y-auto scrollbar w-full">
				{loading && !data ? (
					<div className="flex justify-center py-8">
						<Spinner />
					</div>
				) : error ? (
					<div className="text-center py-8 text-danger">
						Error loading products: {error.message}
					</div>
				) : products.length === 0 ? (
					<div className="text-center py-8 text-gray-500">No products found</div>
				) : (
					<CheckboxGroup
						classNames={{
							base: "w-full flex flex-col gap-3",
						}}
						value={selectedProductIds}
						onChange={handleSelectionChange}
					>
						{products.map((product: GetProductsType, index: number) => (
							<div key={product._id} ref={index === products.length - 1 ? observerRef : null}>
								<MemoizedProductCheckbox product={product} value={product._id} />
							</div>
						))}

						{loading && (
							<div className="flex justify-center py-4">
								<Spinner size="sm" />
							</div>
						)}
					</CheckboxGroup>
				)}
			</div>

			{/* Validation Error */}
			{errors.productIds && <div className="text-danger text-sm">{errors.productIds.message}</div>}
		</div>
	);
};

export default LookBookStep3;
