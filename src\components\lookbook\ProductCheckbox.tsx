import React, { memo } from "react";
import { Checkbox, User, Chip, cn } from "@heroui/react";
import { CiImageOn } from "react-icons/ci";
import { GetProductsType } from "../../types/productType";

interface ProductCheckboxProps {
  product: GetProductsType;
  value: string;
}

const ProductCheckbox: React.FC<ProductCheckboxProps> = ({ product, value }) => {
  const statusColor = product.stockStatus === "IN_STOCK" ? "success" : "danger";
  const status = product.stockStatus === "IN_STOCK" ? "In Stock" : "Out of Stock";
  
  return (
    <Checkbox
      aria-label={product.name}
      classNames={{
        base: cn(
          "inline-flex max-w-full w-full bg-content1 m-0",
          "hover:bg-content2 items-center justify-start",
          "cursor-pointer rounded-lg gap-2 p-4 border-2 border-transparent",
          "data-[selected=true]:border-primary"
        ),
        label: "w-full",
      }}
      value={value}
    >
      <div className="w-full flex justify-between gap-2">
        <User
          avatarProps={{
            size: "md",
            src: product.assets && product.assets.length > 0 ? product.assets[0].path : undefined,
            fallback: <CiImageOn className="text-gray-400" />,
          }}
          description={
            <span className="text-sm text-gray-500">
              ₹{product.price}
              {product.isOnSale && product.discountedPrice && (
                <span className="ml-2 text-green-600">Sale: ₹{product.discountedPrice}</span>
              )}
            </span>
          }
          name={product.name}
        />
        <div className="flex flex-col items-end gap-1">
          <span className="text-tiny text-default-500">
            {product.categories?.[0]?.name || "No Category"}
          </span>
          <Chip color={statusColor} size="sm" variant="flat">
            {status}
          </Chip>
        </div>
      </div>
    </Checkbox>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(ProductCheckbox);