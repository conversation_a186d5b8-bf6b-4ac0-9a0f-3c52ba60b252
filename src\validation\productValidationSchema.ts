import * as z from "zod";
import { StatusTypes } from "../types/commonTypes";

export const productValidationSchema = z
	.object({
		images: z.instanceof(FileList).optional(),
		videos: z.instanceof(FileList).optional(),
		assets: z
			.array(
				z.object({
					altText: z.string(),
					isFeatured: z.boolean(),
					path: z.string(),
					type: z.enum(["IMAGE", "VIDEO"]),
				})
			)
			.optional(),
		name: z.string().min(1, { message: "Name is required" }),
		ribbon: z
			.object({
				name: z.string(),
				ribbonId: z.string(),
			})
			.optional(),
		description: z.string().min(1, { message: "Description is required" }),
		price: z.number().min(1, { message: "Price is required" }),
		isOnSale: z.boolean().optional(),
		customTexts: z
			.array(
				z.object({
					title: z.string(),
					charLimit: z.number(),
					isRequired: z.boolean(),
				})
			)
			.optional(),
		allowCustomText: z.boolean().optional(),
		productOptions: z
			.array(
				z.object({
					optionName: z.string(),
					choices: z.array(
						z.object({
							name: z.string(),
							images: z.array(z.string()),
						})
					),
					showInProductPageAs: z.string(),
				})
			)
			.optional(),
		status: z.nativeEnum(StatusTypes, { message: "Status is required" }),
		categoryIds: z
			.array(z.string().min(1, { message: "Categories is required" }))
			.min(1, { message: "Categories is required" }),
		trackInventory: z.boolean().optional(),
		discountedPrice: z.number().optional().nullable(),
		saleValue: z.number({ message: "Enter only numbers" }).optional().nullable(),
		saleType: z
			.enum(["PERCENT", "RUPEE"], {
				required_error: "Sale type is required",
				invalid_type_error: "Sale type must be either 'PERCENTAGE' or 'RUPEE'",
			})
			.optional(),
		costOfGoods: z.number().min(1, { message: "Cost of Goods is required" }),
		profit: z.number().optional().nullable(),
		margin: z.number().optional().nullable(),
	})
	.refine(
		(data) => {
			// Check if there are existing assets OR new files being uploaded
			const hasExistingAssets = data.assets && data.assets.length > 0;
			const hasNewImages = data.images && data.images.length > 0;
			const hasNewVideos = data.videos && data.videos.length > 0;

			return hasExistingAssets || hasNewImages || hasNewVideos;
		},
		{
			message: "At least one image or video is required",
			path: ["images"], // Show error on images field
		}
	)
	.refine(
		(data) => {
			// If isOnSale is true, then saleValue should be required and valid
			if (data.isOnSale) {
				return data.saleValue !== null && data.saleValue !== undefined && data.saleValue > 0;
			}
			// If isOnSale is false, saleValue can be null/undefined
			return true;
		},
		{
			message: "Sale value is required when product is on sale",
			path: ["saleValue"], // This will show the error on the saleValue field
		}
);
	
export type ProductSchema = z.infer<typeof productValidationSchema>;
