import { gql } from "@apollo/client";

export const CREATE_LOOKBOOK = gql`
	mutation CreateLookBook($input: LookBookInput!) {
		createLookBook(input: $input) {
			_id
		}
	}
`;

export const UPDATE_LOOKBOOK = gql`
	mutation UpdateLookBook($updateLookBookId: ID!, $input: LookBookInput!) {
		updateLookBook(id: $updateLookBookId, input: $input) {
			_id
			name
			subName
			description
			totalPrice
			status
			assets {
				_id
				altText
				path
				type
				isFeatured
				createdAt
				updatedAt
			}
			sections {
				paragraph
				imageUrl
				videourl
			}
			productIds
		}
	}
`;

export const GET_LOOKBOOK_BY_ID = gql`
	query GetLookBookById($getLookBookByIdId: ID!) {
		getLookBookById(id: $getLookBookByIdId) {
			_id
			createdAt
			description
			isDeleted
			name
			subName
			productIds
			products {
				_id
				name
				price
				productType
				status
				description
			}
			sections {
				paragraph
				imageUrl
				videourl
			}
			status
			totalPrice
			updatedAt
			assets {
				_id
				altText
				path
				type
				isFeatured
				createdAt
				updatedAt
			}
		}
	}
`;

export const GET_LOOKBOOKS = gql`
	query GetLookBooks($limit: Int, $offset: Int, $filter: LookBookFilterInput) {
		getLookBooks(limit: $limit, offset: $offset, filter: $filter) {
			totalCount
			lookBooks {
				_id
				assets {
					_id
					altText
					path
					type
				}
				description
				name
				productIds
				status
				subName
				totalPrice
				sections {
					imageUrl
					paragraph
					videourl
				}
			}
		}
	}
`;

export const DELETE_LOOKBOOK = gql`
	mutation DeleteLookBook($deleteLookBookId: ID!) {
		deleteLookBook(id: $deleteLookBookId)
	}
`;

export const BULK_DELETE_LOOKBOOKS = gql`
	mutation BulkDeleteLookBooks($lookBookIds: [ID]!) {
		bulkDeleteLookBooks(lookBookIds: $lookBookIds)
	}
`;
