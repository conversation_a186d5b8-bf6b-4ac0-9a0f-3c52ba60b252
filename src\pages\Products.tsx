import {
	Button,
	DropdownTrigger,
	Dropdown,
	DropdownMenu,
	DropdownItem,
	Image,
	addToast,
	Chip,
} from "@heroui/react";

import { useCallback, useMemo, useRef } from "react";
import { BiExport, BiPlus } from "react-icons/bi";
import { Lu<PERSON>encil } from "react-icons/lu";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import { useProductModals } from "../hooks/useProductModals";
import { RxDividerVertical } from "react-icons/rx";
import { CgTrashEmpty } from "react-icons/cg";
import TableComponent from "../components/Table";
import { CiExport, CiImageOn, CiImport } from "react-icons/ci";
import { ColumnType, GetProductsType, StatusType } from "../types/productType";
import TopHeader from "../components/table/TopHeader";
import TopDrawer from "../components/TopDrawer";
import { Io<PERSON>hevronDown, IoEyeOffOutline, IoEyeOutline, IoTrash } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import { CSVLink } from "react-csv";
import {
	GET_PRODUCTS,
	DELETE_PRODUCT,
	BULK_UPDATE_PRODUCTS,
	BULK_DELETE_PRODUCTS,
	BULK_DUPLICATE_PRODUCTS,
} from "../graphql/products";
import { useMutation, useQuery } from "@apollo/client";
import { useBoundStore } from "../store/store";
import ModalComponent from "../components/ModalComponent";
import VisibilityChangeModalContent from "../components/products/VisibilityChangeModalContent";
import { LiaRibbonSolid } from "react-icons/lia";
import { BsCurrencyRupee } from "react-icons/bs";
import { HiOutlineDuplicate } from "react-icons/hi";
import { FiAlertTriangle } from "react-icons/fi";
import ChangeBulkPriceModal from "../components/products/ChangeBulkPriceModal";
import { useMutationHandler } from "../hooks/useMutationStatusHandler";
import EditRibbonModal from "../components/products/EditRibbonModal";
import { StatusTypes } from "../types/commonTypes";
import TableFilters from "../components/table/TableFilters";
import { useInfiniteQueryScroll } from "../hooks/useInfiniteQueryScroll";

export const columns: ColumnType[] = [
	{ name: "ID", uid: "_id", sortable: false },
	{ name: "Product Details", uid: "name", sortable: false },
	{ name: "SKU", uid: "sku", sortable: false },
	{ name: "Price", uid: "price", sortable: false },
	{ name: "Inventory", uid: "stockStatus" },
	{ name: "Status", uid: "status", sortable: false },
	{ name: "ACTIONS", uid: "actions" },
];

export const statusOptions: StatusType[] = [
	{ name: "PUBLISHED", uid: StatusTypes.PUBLISHED },
	{ name: "SCHEDULED", uid: StatusTypes.SCHEDULED },
	{ name: "ARCHIVED", uid: StatusTypes.ARCHIVED },
	{ name: "DRAFT", uid: StatusTypes.DRAFT },
];

const Products = () => {
	const navigate = useNavigate();
	const {
		selectedKeys,
		setSelectedKeys,
		deleteProductId,
		setDeleteProductId,
		productVisibleColumns,
		setProductVisibleColumns,
		changePriceInputs,
		setChangePriceType,
		setChangePriceValue,
		filterText,
		setFilterText,
		selectedRibbonId,
		setSelectedRibbonId,
		filters,
		setFilters,
	} = useBoundStore();

	// Use the custom hook to manage all modal states
	const {
		mainModal,
		confirmationModal,
		visibilityModal,
		visibilityHiddenModal,
		duplicateModal,
		changePriceModal,
		ribbonEditModal,
		bulkDeleteModal,
	} = useProductModals();

	const { data, loading, fetchMore, refetch, error } = useQuery(GET_PRODUCTS, {
		variables: {
			limit: 10,
			offset: 0,
			filters: { search: filterText },
		},
		fetchPolicy: "cache-and-network", // use 'cache-and-network' or 'network-only'
		notifyOnNetworkStatusChange: true,
	});

	// used to delete the product from the table delete button click
	const [deleteProduct, { data: deleteData, loading: deleteLoading, error: deleteError }] =
		useMutation(DELETE_PRODUCT, {
			refetchQueries: [GET_PRODUCTS, "GetProducts"],
		});

	// used to update the product visiblity in bulk update
	const [
		bulkDuplicateProducts,
		{ data: bulkDuplicateData, loading: bulkDuplicateDataLoading, error: bulkDuplicateError },
	] = useMutation(BULK_DUPLICATE_PRODUCTS, {
		refetchQueries: [GET_PRODUCTS, "GetProducts"],
	});

	// used to update the products in bulk update
	const [
		bulkUpdateProduct,
		{ data: bulkUpdateData, loading: bulkUpdateDataLoading, error: bulkUpdateError },
	] = useMutation(BULK_UPDATE_PRODUCTS, {
		refetchQueries: [GET_PRODUCTS, "GetProducts"],
	});

	const [
		bulkDeleteProducts,
		{ loading: bulkDeleteDataLoading, error: bulkDeleteError, data: bulkDeleteData },
	] = useMutation(BULK_DELETE_PRODUCTS, {
		refetchQueries: [GET_PRODUCTS, "GetProducts"],
	});

	useMutationHandler({
		data: bulkDuplicateData,
		loading: bulkDuplicateDataLoading,
		error: bulkDuplicateError,
		successMessage: "Products duplicated successfully",
		onClose: duplicateModal.onClose,
	});

	useMutationHandler({
		data: deleteData,
		loading: deleteLoading,
		error: deleteError,
		successMessage: "Product deleted successfully",
		onClose: confirmationModal.onClose,
	});

	useMutationHandler({
		data: bulkUpdateData,
		loading: bulkUpdateDataLoading,
		error: bulkUpdateError,
		successMessage: "Products data updated successfully",
		onClose: changePriceModal.onClose,
	});

	useMutationHandler({
		data: bulkDeleteData,
		loading: bulkDeleteDataLoading,
		error: bulkDeleteError,
		successMessage: "Products deleted successfully",
		onClose: bulkDeleteModal.onClose,
	});

	// Get totalCount for display
	const totalCount = data?.getProducts?.totalCount || 0;

	// Use infinite scroll hook like in Orders page
	const scrollContainerRef = useRef<HTMLDivElement | null>(null);
	const { hasMore } = useInfiniteQueryScroll({
		items: data?.getProducts?.products || [],
		totalCount,
		loading,
		fetchMore,
		scrollContainerRef,
	});

	// Determine if this is initial loading (no items) vs infinite scroll loading (has items)
	const isInitialLoading = loading && (data?.getProducts?.products || []).length === 0;

	// used for rendering the cell data in the table
	const renderCell = useCallback(
		(item: GetProductsType, columnKey: string) => {
			const cellValue = item[columnKey as keyof GetProductsType];
			switch (columnKey) {
				case "name":
					return (
						<div className="flex items-center gap-x-2">
							{item.assets && item.assets.length > 0 ? (
								<Image
									src={item.assets[0].path}
									alt={item.name}
									height={50}
									width={50}
									className="w-12 h-12 object-cover rounded"
								/>
							) : (
								<CiImageOn height={50} width={50} />
							)}
							<div>
								<h5>{item.name}</h5>
								<p className="text-textPlaceHolder text-small">
									{item.variants?.length} Variations |{" "}
									{item.categories?.map((c) => c.name).join(", ")}
								</p>
							</div>
						</div>
					);
				case "stockStatus":
					return item.stockStatus === "IN_STOCK" ? "In Stock" : "Out Of Stock";
				case "status":
					return <Chip className="bg-lightPrimary dark:bg-gray-700">{item.status || "DRAFT"}</Chip>;
				case "actions":
					return (
						<div className="relative flex items-center justify-center gap-3">
							<Button
								isIconOnly
								size="sm"
								variant="ghost"
								color="primary"
								className="bg-lightPrimary dark:bg-gray-700 border-none hover:bg-primary group rounded-full p-0 min-w-0 h-auto w-auto"
								onPress={() => navigate(`/admin/store/edit-product/${item._id}`)}
							>
								<LuPencil className="text-primary group-hover:text-white m-1.5" />
							</Button>
							<Button
								isIconOnly
								size="sm"
								variant="ghost"
								color="primary"
								className="bg-lightPrimary dark:bg-gray-700 border-none hover:bg-primary group rounded-full p-0 min-w-0 h-auto w-auto"
								onPress={() => {
									setDeleteProductId(item._id);
									confirmationModal.onOpen();
								}}
							>
								<CgTrashEmpty className="text-primary text-xl group-hover:text-white m-1.5" />
							</Button>
						</div>
					);
				default:
					if (typeof cellValue === "object" && Array.isArray(cellValue)) {
						return (
							<div>
								{cellValue.map((val: unknown, idx: number) => (
									<span key={idx}>{JSON.stringify(val)}</span>
								))}
							</div>
						);
					}
					return typeof cellValue === "object" ? JSON.stringify(cellValue) : cellValue;
			}
		},
		[navigate, setDeleteProductId, confirmationModal]
	);

	// handling the product export in csv format
	const handlePhysicalProdCSVExport = () => {
		const headers = [
			{ label: "ID", key: "_id" },
			{ label: "Name", key: "name" },
			{ label: "Images", key: "images" },
			{ label: "Price", key: "price" },
			{ label: "Discounted Price", key: "discountedPrice" },
			{ label: "OnSale", key: "isOnSale" },
			{ label: "Collections", key: "categories" },
			{ label: "Variants", key: "variants" },
			{ label: "Track Inventory", key: "trackInventory" },
			{ label: "Total Product Quantity", key: "totalProductQuantity" },
			{ label: "Stock Status", key: "stockStatus" },
		];

		const finalData = (data?.getProducts?.products || [])
			.filter((item: GetProductsType) => selectedKeys !== "all" && selectedKeys.has(item._id))
			.map((product: GetProductsType) => ({
				_id: product._id,
				name: product.name,
				images: product.assets.map((asset) => asset.path),
				price: product.price,
				discountedPrice: product.discountedPrice,
				isOnSale: product.isOnSale,
				categories: product.categories ? product.categories.map((category) => category.name) : "",
				variants: product.variants ? product.variants.length : 0,
				trackInventory: product.trackInventory,
				totalProductQuantity: product.totalProductQuantity,
				stockStatus: product.stockStatus,
			}));

		return {
			data: finalData,
			headers,
		};
	};
	const handleProductImport = () => {};

	// handling of the export and import process
	const exportImportDropDownItems = [
		{
			key: "export",
			label: "Export",
			description: "Export your physical products to a CSV file",
			icon: <CiExport className="text-lg" />,
			onPress: handlePhysicalProdCSVExport,
		},
		{
			key: "import",
			label: "Import",
			description: "Import Multiple Product in your store",
			icon: <CiImport className="text-lg" />,
			onPress: handleProductImport,
		},
	];

	// top header content for the table
	const topHeader = useMemo(() => {
		return (
			<TopHeader
				columns={columns}
				exportImportDropDownItems={exportImportDropDownItems}
				filterValue={filterText}
				onOpen={mainModal.onOpen}
				onSearchChange={setFilterText}
				setVisibleColumns={(cols) => setProductVisibleColumns(cols)}
				visibleColumns={productVisibleColumns}
			/>
		);
	}, [productVisibleColumns, filterText, mainModal.onOpen]);

	// top header when any of the row is selected
	const topSelectedContent = useMemo(() => {
		return (
			<div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 p-2 rounded-md">
				<p>
					{selectedKeys === "all" ? data?.getProducts?.products.length : selectedKeys.size} of{" "}
					{data?.getProducts?.products.length} Selected
				</p>
				<RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
				<div className="flex flex-wrap md:flex-nowrap gap-x-3 gap-y-2">
					<Button
						radius="full"
						color="primary"
						size="sm"
						onPress={handlePhysicalProdCSVExport}
						variant="ghost"
						className="border"
						startContent={<BiExport />}
					>
						<CSVLink
							asyncOnClick={true}
							filename="products-export.csv"
							headers={handlePhysicalProdCSVExport().headers}
							data={handlePhysicalProdCSVExport().data}
						>
							Export
						</CSVLink>
					</Button>
					<Dropdown>
						<DropdownTrigger className="flex !rounded-[40px]">
							<Button
								startContent={<IoEyeOutline className="text-small " />}
								variant="ghost"
								size="sm"
								color="primary"
								className="border"
								endContent={<IoChevronDown />}
							>
								Set Visibility
							</Button>
						</DropdownTrigger>
						<DropdownMenu
							disallowEmptySelection
							aria-label="Table Columns"
							closeOnSelect={true}
							selectedKeys={productVisibleColumns}
							selectionMode="multiple"
							classNames={{
								list: "w-full flex-none",
							}}
						>
							<DropdownItem
								key={"showInOnlineStore"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<IoEyeOutline />}
								onPress={() => visibilityModal.onOpen()}
							>
								Show in Online Store
							</DropdownItem>
							<DropdownItem
								key={"hideInOnlineStore"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:hover:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<IoEyeOffOutline />}
								onPress={() => visibilityHiddenModal.onOpen()}
							>
								Hide in Online Store
							</DropdownItem>
						</DropdownMenu>
					</Dropdown>
					<Dropdown>
						<DropdownTrigger className="flex !rounded-[40px]">
							<Button
								startContent={<IoEyeOutline className="text-small " />}
								variant="ghost"
								size="sm"
								color="primary"
								className="border"
								endContent={<IoChevronDown />}
							>
								More Actions
							</Button>
						</DropdownTrigger>
						<DropdownMenu
							disallowEmptySelection
							aria-label="Table Columns"
							closeOnSelect={true}
							//selectedKeys={productVisibleColumns}
							selectionMode="multiple"
							classNames={{
								list: "w-full flex-none",
							}}
						>
							<DropdownItem
								key={"duplicate"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<HiOutlineDuplicate />}
								onPress={() => duplicateModal.onOpen()}
							>
								Duplicate
							</DropdownItem>
							<DropdownItem
								key={"changePrice"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<BsCurrencyRupee />}
								onPress={() => changePriceModal.onOpen()}
							>
								Change Price
							</DropdownItem>
							<DropdownItem
								key={"editRibbon"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<LiaRibbonSolid />}
								onPress={() => ribbonEditModal.onOpen()}
							>
								Edit Ribbon
							</DropdownItem>
							<DropdownItem
								key={"delete"}
								className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
									wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
								}}
								startContent={<IoTrash />}
								onPress={() => bulkDeleteModal.onOpen()}
							>
								Delete
							</DropdownItem>
						</DropdownMenu>
					</Dropdown>
				</div>
			</div>
		);
	}, [selectedKeys, data?.getProducts?.products]);

	const finalSelectedKeys = useMemo(() => {
		if (selectedKeys === "all") {
			return (data?.getProducts?.products || []).map((item: GetProductsType) => item._id);
		}
		return Array.from(selectedKeys.values());
	}, [selectedKeys, data?.getProducts?.products]);

	const handleAction = () => {
		refetch({
			filters: {
				status: filters.status,
				stockStatus: filters.stockStatus,
				productType: filters.productType,
				search: filterText,
			},
		}).then(() => mainModal.onClose());
	};

	if (!loading && error) {
		addToast({
			title: "Failed to laod products",
			color: "danger",
			shouldShowTimeoutProgress: true,
		});
	}

	return (
		<>
			<div>
				<Breadcrumbs>
					<BreadcrumbItem>Home</BreadcrumbItem>
					<BreadcrumbItem>Products</BreadcrumbItem>
				</Breadcrumbs>
			</div>

			<ModalComponent
				isOpen={confirmationModal.isOpen}
				onOpenChange={confirmationModal.onOpenChange}
				id="modal-2"
				onPress={() => deleteProduct({ variables: { deleteProductId } })}
				modalHeader={"Product Delete Confirmation"}
				saveButtonText={"Delete"}
				className={{
					saveColor: "danger",
				}}
				isLoading={deleteLoading}
			>
				Are you sure you want to delete this product
			</ModalComponent>
			<ModalComponent
				isOpen={visibilityModal.isOpen}
				onOpenChange={visibilityModal.onOpenChange}
				id="modal-3"
				size="lg"
				modalHeader={"Change Visibility"}
				isLoading={bulkUpdateDataLoading}
				onPress={() =>
					bulkUpdateProduct({
						variables: {
							productIds: finalSelectedKeys,
							input: {
								status: StatusTypes.PUBLISHED,
							},
						},
					}).then((res) => {
						if (res.data) {
							visibilityModal.onClose();
						}
					})
				}
			>
				<VisibilityChangeModalContent />
			</ModalComponent>
			<ModalComponent
				isOpen={visibilityHiddenModal.isOpen}
				onOpenChange={visibilityHiddenModal.onOpenChange}
				id="modal-4"
				size="lg"
				modalHeader={"Change Visibility"}
				isLoading={bulkUpdateDataLoading}
				onPress={() =>
					bulkUpdateProduct({
						variables: {
							productIds: finalSelectedKeys,
							input: {
								status: StatusTypes.DRAFT,
							},
						},
					}).then((res) => {
						if (res.data) {
							visibilityHiddenModal.onClose();
						}
					})
				}
			>
				<VisibilityChangeModalContent />
			</ModalComponent>
			<ModalComponent
				isOpen={duplicateModal.isOpen}
				onOpenChange={duplicateModal.onOpenChange}
				id="modal-5"
				size="lg"
				// modalHeader={"Change Visibility"}
				isLoading={false}
				onPress={() => {
					bulkDuplicateProducts({
						variables: {
							productIds: finalSelectedKeys,
						},
					});
				}}
			>
				<div className="flex flex-col items-center gap-y-2">
					<FiAlertTriangle className="text-3xl" />
					<h5>Are you sure you want to duplicate this product?</h5>
				</div>
			</ModalComponent>
			<ModalComponent
				isOpen={changePriceModal.isOpen}
				onOpenChange={changePriceModal.onOpenChange}
				id="modal-6"
				size="3xl"
				modalHeader={"Change the price of the products"}
				isLoading={bulkUpdateDataLoading}
				onPress={() => {
					bulkUpdateProduct({
						variables: {
							productIds: finalSelectedKeys,
							input: {
								changePrice: {
									type: changePriceInputs.type,
									value: changePriceInputs.value,
								},
							},
						},
					}).then(() => {
						setChangePriceType(null);
						setChangePriceValue(null);
						changePriceModal.onClose();
					});
				}}
			>
				<ChangeBulkPriceModal />
			</ModalComponent>
			<ModalComponent
				isOpen={ribbonEditModal.isOpen}
				onOpenChange={ribbonEditModal.onOpenChange}
				id="modal-7"
				size="lg"
				modalHeader={"Add a ribbon to products "}
				isLoading={false}
				disabled={selectedRibbonId === null || selectedRibbonId === ""}
				onPress={() => {
					//update product ribbon
					// check if new ribbon then  add the new one

					bulkUpdateProduct({
						variables: {
							productIds: finalSelectedKeys,
							input: {
								updateRibbon: {
									ribbonId: selectedRibbonId,
								},
							},
						},
					}).then(() => {
						ribbonEditModal.onClose();
						setSelectedRibbonId(null);
					});

					//duplicateProductAPICa
				}}
			>
				<EditRibbonModal />
			</ModalComponent>
			<ModalComponent
				isOpen={bulkDeleteModal.isOpen}
				onOpenChange={bulkDeleteModal.onOpenChange}
				id="modal-8"
				size="lg"
				// modalHeader={"Change Visibility"}
				isLoading={false}
				onPress={() => {
					bulkDeleteProducts({
						variables: {
							productIds: finalSelectedKeys,
						},
					}).then(() => {
						bulkDeleteModal.onClose();
					});
				}}
			>
				<div className="flex flex-col items-center gap-y-2">
					<FiAlertTriangle className="text-3xl text-red-500" />
					<h5>Are you sure you want to delete the selected products?</h5>
				</div>
			</ModalComponent>

			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<h1>Products {totalCount ? `(${totalCount})` : null}</h1>
				<Button
					size="sm"
					radius="full"
					color="primary"
					startContent={<BiPlus />}
					onPress={() => navigate("/admin/store/add-product")}
				>
					Add New Product
				</Button>
			</div>
			<TopDrawer
				isOpen={mainModal.isOpen}
				onOpenChange={mainModal.onOpenChange}
				drawerHeader="Filters"
				isLoading={loading}
				handleAction={handleAction}
			>
				<TableFilters filters={filters} setFilters={setFilters} />{" "}
			</TopDrawer>
			<TableComponent<GetProductsType>
				columns={columns}
				isLoading={isInitialLoading}
				hasMore={hasMore}
				list={{ items: data?.getProducts?.products || [] }}
				renderCell={renderCell}
				visibleColumns={productVisibleColumns}
				topContent={topHeader}
				topSelectedContent={topSelectedContent}
				selectedKeys={selectedKeys}
				setSelectedKeys={(keys) => setSelectedKeys(keys)}
				handleLoadMore={() =>
					fetchMore({
						variables: {
							offset: data?.getProducts?.products?.length || 0,
						},
					})
				}
			/>
		</>
	);
};

export default Products;
