import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input, Button, Form, addToast } from "@heroui/react";
import { z } from "zod";
import { gql, useLazyQuery, useApolloClient } from "@apollo/client";
import { useNavigate } from "react-router-dom";
import { useStore } from "zustand";
import { useBoundStore } from "../store/store";

const signInSchema = z.object({
	email: z.string().email({ message: "Invalid email address" }),
	password: z.string().min(8, { message: "Password must be at least 8 characters" }),
});

type SignInFormData = {
	email: string;
	password: string;
};

const SIGN_IN_QUERY = gql(`query ExampleQuery($email: String!, $password: String!) {
  signIn(email: $email, password: $password) {
    firstname
    lastname
    email
    phoneno
    profileImg
    isSuperAdmin
    clientName
    clientLink
    dbToken
    authToken
  }
}`);

export default function SignInForm() {
	const navigate = useNavigate();
	const setUser = useBoundStore((state) => state.setUser);
	const apolloClient = useApolloClient();
	const [signIn, { loading, error, data }] = useLazyQuery(SIGN_IN_QUERY);
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<SignInFormData>({
		resolver: zodResolver(signInSchema),
		defaultValues: { email: "", password: "" },
	});

	// first call the api to sign in the user, show toast message, set the user to the store, and finally navigate to the dashboard homepage
	const onSubmit = (values: SignInFormData) => {
		signIn({
			variables: { email: values.email, password: values.password },
		}).then((res) => {
			addToast({
				title: "Login In Success",
				color: "success",
				shouldShowTimeoutProgress: true,
			});
			setUser(res.data.signIn);

			// Clear Apollo cache to ensure fresh data with new tokens
			apolloClient.clearStore().then(() => {
				navigate("/admin/store/products");
			});
		});
	};

	// show the error message
	if (error) {
		console.error("Sign in failed", error);
		addToast({
			title: "Login Failed! Try again",
			color: "danger",
		});
	}

	return (
		<div className="w-full  grid grid-cols-1 md:grid-cols-2 place-items-center h-screen">
			<div className="h-full hidden md:block bg-[url(/images/login-bg.png)] bg-cover bg-no-repeat bg-opacity-40 w-full"></div>
			<div className="px-5 py-10 w-full md:w-96 relative">
				<Form className="flex flex-col gap-5 " onSubmit={handleSubmit(onSubmit)}>
					<h1 className="text-lg font-medium">Log In</h1>
					<Input
						size="sm"
						type="email"
						classNames={{
							inputWrapper: "w-full after:h-[1px] after:bg-primary",
						}}
						placeholder="Email"
						variant="underlined"
						{...register("email")}
						isInvalid={!!errors.email}
						errorMessage={errors.email?.message}
					/>
					<Input
						size="sm"
						type="password"
						classNames={{
							innerWrapper: "dark:bg-slate-900",
							inputWrapper: "w-full after:h-[1px] after:bg-primary",
						}}
						variant="underlined"
						{...register("password")}
						placeholder="Password"
						isInvalid={!!errors.password}
						errorMessage={errors.password?.message}
					/>

					<Button isLoading={loading} className="w-full" color="primary" size="sm" type="submit">
						Login
					</Button>
				</Form>
				<p className="text-center mt-5 text-xs">
					Developed by <span className="font-semibold">Zerror Studios</span>
				</p>
			</div>
		</div>
	);
}
