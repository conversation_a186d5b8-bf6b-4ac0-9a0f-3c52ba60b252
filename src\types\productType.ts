import { StatusTypes, StockStatus } from "./commonTypes";

export interface CustomText {
	charLimit: number;
	isRequired: boolean;
	title: string;
}

export interface OptionChoice {
	images: string[];
	name: string;
}

export interface ColumnType {
	name: string;
	uid: string;
	sortable?: boolean;
}

export interface ProductOptions {
	choices: OptionChoice[];
	optionName: string;
	showInProductPageAs: string;
}
export type CategoriesType = {
	_id?: string;
	name: string;
	imgsrc?: string;
	isDelete?: boolean;
	createdAt?: Date;
	updatedAt?: Date;
};

export type AssetTypeEnum = "IMAGE" | "VIDEO";
export type AssetInputType = {
	altText?: string;
	isFeatured?: boolean;
	path?: string;
	type?: AssetTypeEnum;
};

export type SaleTypeEnum = "PERCENT" | "RUPEE";
export interface ProductSchema {
	assets?: AssetInputType[];
	ribbon?: {
		name: string;
		ribbonId: string;
	};
	costOfGoods: number;
	name: string;
	description: string;
	price: number;
	profit?: number;
	margin?: number;
	// stock: number;
	isOnSale?: boolean;
	allowCustomText?: boolean;
	customTexts?: CustomText[];
	productOptions?: ProductOptions[];
	categoryIds: string[];
	trackInventory?: boolean;
	discountedPrice?: number | null;
	saleValue?: number | null;
	saleType?: SaleTypeEnum;
	images?: FileList;
	videos?: FileList;
	status: StatusTypes;
}

export interface StatusType {
	name: string;
	uid: StatusTypes;
}

export type ProductVariantsType = {
	_id?: string;
	visibility: boolean;
	sku: string;
	priceDifference?: number;
	selectedOptions?: string[];
	shippingWeight?: number;
	status?: StatusTypes;
	stockStatus?: StockStatus;
	stockQuantity?: number;
	variantPrice: number;
	trackInventory?: boolean;
	variantCostOfGoods?: number;
};

export interface GetProductsType {
	_id: string;
	name: string;
	price: number;
	description: string;
	assets: { path: string }[];
	stockStatus: string;
	isDeleted: boolean;
	status: StatusTypes;
	variants: ProductVariantsType[];
	categories: CategoriesType[];
	isOnSale: boolean;
	discountedPrice: number;
	trackInventory: boolean;
	totalProductQuantity: number;
}

export enum ChangePriceType {
	INCREASE_BY_AMOUNT,
	REDUCE_BY_AMOUNT,
	SET_A_NEW_PRICE,
	INCREASE_BY_PERCENTAGE,
	REDUCE_BY_PERCENTAGE,
}
