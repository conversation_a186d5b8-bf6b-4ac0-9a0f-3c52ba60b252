import { But<PERSON>, <PERSON>, Switch, Tooltip } from "@heroui/react";
import React from "react";
import { BiPlus } from "react-icons/bi";
import { IoInformationCircle } from "react-icons/io5";
import { ProductOptions } from "../../types/productType";
import { LuPencil } from "react-icons/lu";
import { HiOutlineTrash } from "react-icons/hi";
import { useBoundStore } from "../../store/store";

type ProductVariantsProps = {
  getValues: (name: string) => any;
  isManageVariants: boolean;
  handleAddProductOptionsModal: () => void;
  onOpen: () => void;
  remove: (index: number) => void;
};
const ProductVariants = ({
  getValues,
  handleAddProductOptionsModal,
  isManageVariants,
  onOpen,
  remove,
}: ProductVariantsProps) => {
  const { setProductOptionIndex, setProductOptions, variants } =
    useBoundStore();

  const handleEditProductOption = (index: number) => {
    onOpen(); // update
    setProductOptionIndex(index);
    setProductOptions(
      getValues("productOptions")?.[index] ?? {
        choices: [],
        optionName: "",
        showInProductPageAs: "",
      }
    );
  };
  return (
    <>
      <div className="grid grid-cols-1 items-center md:grid-cols-[70%_30%]">
        <div className="flex flex-col h-full justify-between">
          <p className="mb-4">
            Does your product come in different options, like size, color or
            material? Add them here.
          </p>
          {getValues("productOptions")?.length === 0 ? (
            <Button
              color="primary"
              className="w-fit"
              onPress={onOpen}
              startContent={<BiPlus />}
              radius="full"
              size="sm"
            >
              Add Options
            </Button>
          ) : null}
          <>
            {getValues("productOptions")?.map(
              (productOption: ProductOptions, index: number) => {
                return (
                  <div
                    key={index}
                    className="w-full grid grid-cols-3 border-t border-b py-4 group items-center"
                  >
                    <div>{productOption.optionName}</div>
                    <div>
                      {productOption.choices
                        .map((value) => value.name)
                        .join(", ")}
                    </div>
                    <div className="opacity-0 group-hover:opacity-100 w-full flex gap-x-3 justify-end">
                      <LuPencil
                        onClick={() => handleEditProductOption(index)}
                        className="text-white group-hover:text-primary text-xl hover:cursor-pointer "
                      />
                      <HiOutlineTrash
                        onClick={() => remove(index)}
                        className="text-white group-hover:text-primary text-xl hover:cursor-pointer "
                      />
                    </div>
                  </div>
                );
              }
            )}
            {(getValues("productOptions")?.length ?? 0) > 0 && (
              <>
                <div className="w-full py-4 border-b">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onOpen();
                    }}
                    className="text-primary flex items-center gap-1 bg-transparent border-none active:outline-none focus:outline-none"
                  >
                    <BiPlus /> <span> Add another option</span>
                  </button>
                </div>
                <div className="flex gap-x-2 pt-4">
                  <Switch
                    size="sm"
                    aria-label="manage product options"
                    isSelected={isManageVariants}
                    className="flex gap-1"
                    onValueChange={handleAddProductOptionsModal}
                  ></Switch>
                  <p className="flex items-center">
                    <span>
                      Manage pricing and inventory for each product variant
                    </span>
                    <span className="inline-block ml-1 ">
                      <Tooltip content="Every time you want to save a choice, press Enter to add it">
                        <IoInformationCircle />
                      </Tooltip>
                    </span>
                  </p>
                </div>
              </>
            )}
          </>
        </div>
        <div className="flex justify-center md:justify-end">
          <Image src="/images/shirts.svg" />
        </div>
      </div>
    </>
  );
};

export default ProductVariants;
