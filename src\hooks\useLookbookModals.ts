import { useDisclosure } from "@heroui/react";

/**
 * Custom hook to manage all modals in the Lookbooks page
 */
export const useLookbookModals = () => {
  // Main modal for filters
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure({
    id: "lookbook-modal-1",
  });

  // Delete confirmation modal
  const {
    isOpen: isConfirmationOpen,
    onOpen: onConfirmationOpen,
    onClose: onConfirmationClose,
    onOpenChange: onConfirmationOpenChange,
  } = useDisclosure({ id: "lookbook-modal-2" });

  // Bulk delete lookbooks modal
  const {
    isOpen: isBulkDeleteOpen,
    onOpen: onBulkDeleteOpen,
    onClose: onBulkDeleteClose,
    onOpenChange: onBulkDeleteOpenChange,
  } = useDisclosure({ id: "lookbook-modal-3" });

  // View lookbook modal
  const {
    isOpen: isViewLookbookOpen,
    onOpen: onViewLookbookOpen,
    onClose: onViewLookbookClose,
    onOpenChange: onViewLookbookOpenChange,
  } = useDisclosure({ id: "lookbook-modal-4" });

  return {
    // Main modal
    mainModal: {
      isOpen,
      onOpen,
      onOpenChange,
      onClose,
    },

    // Delete confirmation modal
    confirmationModal: {
      isOpen: isConfirmationOpen,
      onOpen: onConfirmationOpen,
      onClose: onConfirmationClose,
      onOpenChange: onConfirmationOpenChange,
    },

    // Bulk delete modal
    bulkDeleteModal: {
      isOpen: isBulkDeleteOpen,
      onOpen: onBulkDeleteOpen,
      onClose: onBulkDeleteClose,
      onOpenChange: onBulkDeleteOpenChange,
    },

    // View lookbook modal
    viewLookbookModal: {
      isOpen: isViewLookbookOpen,
      onOpen: onViewLookbookOpen,
      onClose: onViewLookbookClose,
      onOpenChange: onViewLookbookOpenChange,
    },
  };
};