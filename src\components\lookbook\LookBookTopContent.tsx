import { UseFormRegister, UseFormSetValue, UseFormWatch, FieldErrors } from "react-hook-form";
import { CiImageOn } from "react-icons/ci";
import { Image } from "@heroui/react";
import { BiPlus } from "react-icons/bi";

import { LookBookFileInput, LookBookFileInput2 } from "./LookBookFileInput";
import VideoPreview from "../forms/VideoPreview";
import { LookBookFormData } from "../../validation/lookbookValidationSchema";

interface LookBookTopContentProps {
	register: UseFormRegister<LookBookFormData>;
	errors: FieldErrors<LookBookFormData>;
	watch: UseFormWatch<LookBookFormData>;
	setFormValue?: UseFormSetValue<LookBookFormData>;
}

const LookBookTopContent: React.FC<LookBookTopContentProps> = ({
	register,
	errors,
	watch,
	setFormValue,
}) => {
	const images = watch("images") || new DataTransfer().files;
	const videos = watch("videos") || new DataTransfer().files;
	const assets = watch("assets") || [];

	// Handle file input changes for adding more files
	const handleFileChange = (name: keyof LookBookFormData, files: FileList) => {
		if (!files || !setFormValue) return;

		const dataTransfer = new DataTransfer();

		// Add existing files
		if (name === "images") {
			for (let i = 0; i < images.length; i++) {
				dataTransfer.items.add(images[i]);
			}
		}

		if (name === "videos") {
			for (let i = 0; i < videos.length; i++) {
				dataTransfer.items.add(videos[i]);
			}
		}

		// Add new files
		for (let i = 0; i < files.length; i++) {
			dataTransfer.items.add(files[i]);
		}

		// Set the updated FileList
		setFormValue(name, dataTransfer.files);
	};

	const hasFiles = images.length > 0 || videos.length > 0;
	const hasAssets = assets.length > 0;

	return (
		<div className="w-full">
			<div
				className={`${
					hasFiles || hasAssets
						? "flex flex-wrap items-center gap-4 h-full"
						: "grid grid-cols-1 md:grid-cols-2 gap-4"
				}`}
			>
				{/* Display existing assets from edit mode */}
				{hasAssets &&
					assets.map((asset, index) => (
						<div key={`asset-${index}`} className="relative w-40 h-40">
							{asset.type === "IMAGE" ? (
								<Image
									src={asset.path || ""}
									height={150}
									width={150}
									classNames={{
										img: "object-cover h-40 w-40",
									}}
									alt={asset.altText || `Asset ${index}`}
									className="rounded-md w-40 h-40"
								/>
							) : (
								<VideoPreview src={asset.path || ""} />
							)}
						</div>
					))}

				{/* Display new images */}
				{images.length > 0 &&
					Array.from(images).map((file, index) => (
						<div key={`image-${index}`} className="relative w-40 h-40">
							<Image
								src={URL.createObjectURL(file)}
								height={150}
								width={150}
								classNames={{
									img: "object-cover h-40 w-40",
								}}
								alt={`Preview ${index}`}
								className="rounded-md w-40 h-40"
							/>
						</div>
					))}

				{/* Display new videos */}
				{videos.length > 0 &&
					Array.from(videos).map((file, index) => (
						<div key={`video-${index}`} className="relative w-40 h-40">
							<VideoPreview src={URL.createObjectURL(file)} />
						</div>
					))}

				{/* Initial file inputs when no files are present */}
				{!hasFiles && !hasAssets && (
					<>
						<div>
							<LookBookFileInput
								register={register}
								Icon={CiImageOn}
								titleText="Add Images"
								name="images"
								accept="image/*"
							/>
							{errors.images && <small className="px-4 text-danger">{errors.images.message}</small>}
						</div>
						<div>
							<LookBookFileInput
								register={register}
								Icon={CiImageOn}
								titleText="Add Videos"
								name="videos"
								accept="video/*"
							/>
						</div>
					</>
				)}

				{/* Add more files button when files are present */}
				{(hasFiles || hasAssets) && (
					<div
						className="flex items-center justify-center -mt-2"
						style={{ height: 152, width: 155 }}
					>
						<div className="bg-white h-full w-full rounded-md flex flex-col justify-center gap-y-2 p-3">
							<div className="bg-lightPrimary p-1 rounded-md">
								<LookBookFileInput2
									handleFileChange={handleFileChange}
									Icon={BiPlus}
									titleText="Add Images"
									name="images"
									accept="image/*"
								/>
							</div>
							<div className="bg-lightPrimary p-1 rounded-md">
								<LookBookFileInput2
									handleFileChange={handleFileChange}
									Icon={BiPlus}
									accept="video/*"
									titleText="Add Videos"
									name="videos"
								/>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Validation error for required files */}
			{errors.images && (
				<div className="mt-2">
					<small className="text-danger">{errors.images.message}</small>
				</div>
			)}
		</div>
	);
};

export default LookBookTopContent;
