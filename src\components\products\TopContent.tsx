import { UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { CiImageOn } from "react-icons/ci";

import { FieldErrors } from "react-hook-form";
import { CustomFileInput, CustomFileInput2 } from "../forms/CustomInput";
import { ProductSchema } from "../../types/productType";
import { Image } from "@heroui/react";
import { BiPlus } from "react-icons/bi";
import VideoPreview from "../forms/VideoPreview";
import { RiCloseCircleFill } from "react-icons/ri";

interface TopContentProps {
	register: UseFormRegister<ProductSchema>;
	errors: FieldErrors<ProductSchema>;
	watch: UseFormWatch<ProductSchema>;
	setFormValue?: UseFormSetValue<ProductSchema>;
}
const TopContent = ({ register, errors, watch, setFormValue }: TopContentProps) => {
	const images = watch("images") as FileList;
	const videos = watch("videos") as FileList;
	const assets = watch("assets") || [];

	// Function to remove existing asset
	const removeExistingAsset = (indexToRemove: number) => {
		if (!setFormValue) return;
		const updatedAssets = assets.filter((_, index) => index !== indexToRemove);
		setFormValue("assets", updatedAssets);
	};

	// Function to remove new image file
	const removeNewImage = (indexToRemove: number) => {
		if (!setFormValue) return;
		const dt = new DataTransfer();
		Array.from(images).forEach((file, index) => {
			if (index !== indexToRemove) {
				dt.items.add(file);
			}
		});
		setFormValue("images", dt.files);
	};

	// Function to remove new video file
	const removeNewVideo = (indexToRemove: number) => {
		if (!setFormValue) return;
		const dt = new DataTransfer();
		Array.from(videos).forEach((file, index) => {
			if (index !== indexToRemove) {
				dt.items.add(file);
			}
		});
		setFormValue("videos", dt.files);
	};

	// handling the file input here for the videos and the images
	const handleFileChange = (name: keyof ProductSchema, files: FileList) => {
		if (!files) return;

		const dataTransfer = new DataTransfer();

		// Add existing files
		if (name === "images") {
			for (let i = 0; i < images.length; i++) {
				dataTransfer.items.add(images[i]);
			}
		}

		if (name === "videos") {
			for (let i = 0; i < videos.length; i++) {
				dataTransfer.items.add(videos[i]);
			}
		}

		// Add new files
		for (let i = 0; i < files.length; i++) {
			dataTransfer.items.add(files[i]);
		}
		// Set the updated FileList
		if (setFormValue) {
			setFormValue(name, dataTransfer.files);
		}
	};

	// Separate existing assets by type
	const existingImages = assets.filter((asset) => asset.type === "IMAGE");
	const existingVideos = assets.filter((asset) => asset.type === "VIDEO");

	// Check if we have any content to display
	const hasContent = images.length > 0 || videos.length > 0 || assets.length > 0;

	return (
		<div className="w-full">
			<div
				className={`${
					hasContent
						? "flex flex-wrap items-center  gap-4 h-full"
						: "grid grid-cols-1 md:grid-cols-2 gap-4 "
				}`}
			>
				{/* Display existing images from assets */}
				{existingImages.map((asset, index) => {
					const originalIndex = assets.findIndex(
						(a) => a.path === asset.path && a.type === "IMAGE"
					);
					return (
						<div
							key={`existing-image-${asset.path}-${index}`}
							className="group w-40 h-40 group relative"
						>
							<Image
								src={asset.path}
								height={150}
								width={150}
								classNames={{
									img: "object-cover h-40 w-40",
								}}
								alt={asset.altText || `Existing image ${index}`}
								className="rounded-md w-40 h-40"
							/>
							<RiCloseCircleFill
								onClick={() => removeExistingAsset(originalIndex)}
								className="absolute  bg-white  rounded-full p-0 m-0 top-0 right-0 text-2xl text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer z-10"
							/>
						</div>
					);
				})}

				{/* Display new images from FileList */}
				{images.length > 0 &&
					Array.from(images).map((file, index) => (
						<div key={`new-image-${index}`} className="relative w-40 group h-40 group">
							<Image
								src={URL.createObjectURL(file)}
								height={150}
								width={150}
								classNames={{
									img: "object-cover h-40 w-40",
								}}
								alt={`Preview ${index}`}
								className="rounded-md w-40 h-40"
							/>
							<RiCloseCircleFill
								onClick={() => removeNewImage(index)}
								className="absolute  text-2xl top-0 right-0 text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer z-10"
							/>
						</div>
					))}

				{/* Display existing videos from assets */}
				{existingVideos.map((asset, index) => {
					const originalIndex = assets.findIndex(
						(a) => a.path === asset.path && a.type === "VIDEO"
					);
					return (
						<div key={`existing-video-${asset.path}-${index}`} className="relative w-40 h-40 group">
							<VideoPreview src={asset.path} />
							<RiCloseCircleFill
								onClick={() => removeExistingAsset(originalIndex)}
								className="absolute  text-2xl bg-white rounded-full p-0 m-0 top-0 right-2 text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer z-10"
							/>
						</div>
					);
				})}

				{/* Display new videos from FileList */}
				{videos.length > 0 &&
					Array.from(videos).map((file, index) => (
						<div key={`new-video-${index}`} className="relative w-40 h-40 group">
							<VideoPreview src={URL.createObjectURL(file)} />
							<RiCloseCircleFill
								onClick={() => removeNewVideo(index)}
								className="absolute  text-2xl bg-white rounded-full p-0 m-0 top-0 right-2 text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer z-10"
							/>
						</div>
					))}

				{/* Show file input when no content exists */}
				{!hasContent && (
					<>
						<div>
							<CustomFileInput
								register={register}
								Icon={CiImageOn}
								titleText="Add Images"
								name="images"
								accept="image/*"
							/>
							{errors.images ? (
								<small className="px-4 text-danger">{errors.images.message}</small>
							) : null}
						</div>
						<div>
							<CustomFileInput
								register={register}
								Icon={CiImageOn}
								titleText="Add Videos"
								name="videos"
								accept="video/*"
							/>
						</div>
					</>
				)}

				{/* Show add more button when content exists */}
				{hasContent && (
					<div
						className="flex items-center justify-center -mt-2"
						style={{ height: 152, width: 155 }}
					>
						<div className="bg-lightPrimary h-full w-full rounded-md flex flex-col justify-center gap-y-2 p-3 ">
							<CustomFileInput2
								handleFileChange={handleFileChange}
								register={register}
								Icon={BiPlus}
								titleText="Add Images"
								name="images"
							/>
							<CustomFileInput2
								handleFileChange={handleFileChange}
								register={register}
								Icon={BiPlus}
								accept="video/*"
								titleText="Add Videos"
								name="videos"
							/>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default TopContent;
