import {
	Button,
	Dropdown,
	DropdownItem,
	Dropdown<PERSON>enu,
	DropdownTrigger,
	Image,
} from "@heroui/react";
import { CiImageOn } from "react-icons/ci";
import { HiDotsHorizontal } from "react-icons/hi";
import { BiTrash } from "react-icons/bi";
import { CartItem as CartItemType } from "../../types/orderType";
import { UseFormSetValue, UseFormWatch } from "react-hook-form";
import { OrderSchema } from "../../types/orderType";

interface CartItemProps {
	item: CartItemType;
	index: number;
	setValue: UseFormSetValue<OrderSchema>;
	watch: UseFormWatch<OrderSchema>;
}

const CartItem = ({ item, index, setValue, watch }: CartItemProps) => {
	const handleRemoveItem = () => {
		const updatedCart = watch("cart").filter((cartItem: CartItemType) => cartItem !== item);
		setValue("cart", updatedCart);
	};

	return (
		<div key={index} className="grid grid-cols-1 md:grid-cols-3 w-full">
			<div className="flex items-center gap-2 justify-start">
				{item.asset?.path ? (
					<Image
						src={item.asset?.path || "/images/placeholder.svg"}
						alt="product"
						className="!w-10 !h-10 rounded-md"
					/>
				) : (
					<CiImageOn className="text-[#3b82f6] w-10 h-10 m-auto" />
				)}
				<div className="flex flex-col">
					<span className="text-sm">{item.name}</span>
					{item.variantDetail?.selectedOptions?.join(", ") ? (
						<span className="text-tiny capitalize">
							{item.variantDetail?.selectedOptions?.join(" | ")}
						</span>
					) : null}
				</div>
			</div>
			<div className="flex gap-x-2 items-center justify-end">
				<div>Qty: {`${item.qty}`}</div>
			</div>
			<div className="flex gap-x-2 items-center justify-end">
				<div>{`₹ ${(item.variantDetail?.variantPrice
					? item.variantDetail.variantPrice * item.qty
					: item.price * item.qty
				).toFixed(2)}`}</div>
				<div>
					<Dropdown>
						<DropdownTrigger className="hidden sm:flex !rounded-[40px]">
							<Button
								isIconOnly
								color="primary"
								variant="ghost"
								className="border w-6 h-6 min-w-6 min-h-6 rounded-full"
								size="sm"
								radius="full"
								startContent={<HiDotsHorizontal />}
							/>
						</DropdownTrigger>
						<DropdownMenu disallowEmptySelection closeOnSelect={true}>
							{/* <DropdownItem
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[selectable=true]:focus:bg-lightPrimary",
								}}
								startContent={<BiPencil />}
								key="edit"
								className="capitalize"
								onPress={() => {
									setCartItem(item);
									productSelectModal.onOpen();
								}}
							>
								Edit
							</DropdownItem> */}
							<DropdownItem
								startContent={<BiTrash />}
								onPress={handleRemoveItem}
								key="remove"
								className="capitalize"
								classNames={{
									base: "data-[hover=true]:bg-lightPrimary data-[selectable=true]:focus:bg-lightPrimary",
								}}
							>
								Remove
							</DropdownItem>
						</DropdownMenu>
					</Dropdown>
				</div>
			</div>
		</div>
	);
};

export default CartItem;
